import Phaser from 'phaser';

export default class Food extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y, config = {}) {
        // Determine texture based on color
        const texture = Food.getTextureForColor(config.color || 'pink');
        
        super(scene, x, y, texture);
        
        // Add to scene
        scene.add.existing(this);
        scene.physics.add.existing(this);
        
        // Food properties
        this.foodConfig = {
            id: config.id || Phaser.Math.RND.uuid(),
            color: config.color || 'pink',
            size: config.size || 1,
            value: config.value || 10,
            ...config
        };
        
        // Set up physics
        this.setupPhysics();
        
        // Set up appearance
        this.setupAppearance();
        
        // Add floating animation
        this.addFloatingAnimation();
        
        // Add glow effect
        this.addGlowEffect();
        
        console.log(`Food created: ${this.foodConfig.color} (${this.foodConfig.size})`);
    }

    static getTextureForColor(color) {
        const colorMap = {
            'pink': 'food_pink',
            'blue': 'food_blue', 
            'green': 'food_green',
            'lime': 'food_lime',
            'red': 'food_red'
        };
        
        return colorMap[color] || 'food_pink';
    }

    static getSizeForColor(color) {
        const sizeMap = {
            'pink': 1,
            'blue': 5,
            'green': 5, 
            'lime': 10,
            'red': 15
        };
        
        return sizeMap[color] || 1;
    }

    static getValueForColor(color) {
        const valueMap = {
            'pink': 10,
            'blue': 50,
            'green': 50,
            'lime': 100,
            'red': 150
        };
        
        return valueMap[color] || 10;
    }

    setupPhysics() {
        // Set collision body
        const radius = Math.max(8, this.foodConfig.size * 2);
        this.body.setCircle(radius);
        
        // Food doesn't move
        this.body.setImmovable(true);
        this.body.setVelocity(0, 0);
        
        // Set collision category
        this.body.setCollisionCategory(2); // Food collision category
    }

    setupAppearance() {
        // Set scale based on size
        const baseScale = 1;
        const sizeMultiplier = Math.max(0.5, Math.min(2, this.foodConfig.size / 8));
        this.setScale(baseScale * sizeMultiplier);
        
        // Set origin to center
        this.setOrigin(0.5, 0.5);
        
        // Set depth for layering
        this.setDepth(1);
        
        // Add slight rotation for variety
        this.setRotation(Phaser.Math.FloatBetween(0, Math.PI * 2));
    }

    addFloatingAnimation() {
        // Gentle floating animation
        this.scene.tweens.add({
            targets: this,
            y: this.y - 3,
            duration: 2000 + Phaser.Math.Between(-500, 500),
            ease: 'Sine.easeInOut',
            yoyo: true,
            repeat: -1
        });
        
        // Gentle rotation
        this.scene.tweens.add({
            targets: this,
            rotation: this.rotation + Math.PI * 2,
            duration: 8000 + Phaser.Math.Between(-2000, 2000),
            ease: 'Linear',
            repeat: -1
        });
    }

    addGlowEffect() {
        // Add glow effect for larger food items
        if (this.foodConfig.size >= 5) {
            // Create glow sprite behind the food
            const glowTexture = this.scene.add.graphics();
            glowTexture.fillStyle(this.getGlowColor(), 0.3);
            glowTexture.fillCircle(0, 0, this.displayWidth * 0.8);
            glowTexture.generateTexture('glow_' + this.foodConfig.id, this.displayWidth * 1.6, this.displayHeight * 1.6);
            glowTexture.destroy();
            
            this.glowSprite = this.scene.add.sprite(this.x, this.y, 'glow_' + this.foodConfig.id);
            this.glowSprite.setDepth(0);
            this.glowSprite.setAlpha(0.6);
            
            // Pulsing glow animation
            this.scene.tweens.add({
                targets: this.glowSprite,
                scaleX: 1.2,
                scaleY: 1.2,
                alpha: 0.3,
                duration: 1500,
                ease: 'Sine.easeInOut',
                yoyo: true,
                repeat: -1
            });
        }
    }

    getGlowColor() {
        const colorMap = {
            'pink': 0xff6b6b,
            'blue': 0x4ecdc4,
            'green': 0x45b7d1,
            'lime': 0x96ceb4,
            'red': 0xffeaa7
        };
        
        return colorMap[this.foodConfig.color] || 0xff6b6b;
    }

    consume() {
        console.log(`Food consumed: ${this.foodConfig.color} (+${this.foodConfig.value} points)`);
        
        // Create consumption effect
        this.createConsumptionEffect();
        
        // Remove glow if it exists
        if (this.glowSprite) {
            this.glowSprite.destroy();
        }
        
        // Emit consumption event
        this.scene.events.emit('foodConsumed', {
            id: this.foodConfig.id,
            color: this.foodConfig.color,
            size: this.foodConfig.size,
            value: this.foodConfig.value,
            x: this.x,
            y: this.y
        });
        
        // Remove from scene
        this.destroy();
        
        return this.foodConfig.value;
    }

    createConsumptionEffect() {
        // Particle burst effect
        const particles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 30, max: 80 },
            scale: { start: 0.3, end: 0 },
            tint: this.getGlowColor(),
            lifespan: 600,
            quantity: 8 + this.foodConfig.size
        });
        
        // Scale up and fade out effect
        this.scene.tweens.add({
            targets: this,
            scaleX: this.scaleX * 1.5,
            scaleY: this.scaleY * 1.5,
            alpha: 0,
            duration: 200,
            ease: 'Power2.easeOut'
        });
        
        // Remove particles after animation
        this.scene.time.delayedCall(600, () => {
            particles.destroy();
        });
    }

    updatePosition(x, y) {
        this.setPosition(x, y);
        
        if (this.glowSprite) {
            this.glowSprite.setPosition(x, y);
        }
    }

    // Static factory methods
    static createRandomFood(scene, worldBounds) {
        const colors = ['pink', 'blue', 'green', 'lime', 'red'];
        const weights = [60, 20, 15, 4, 1]; // Pink is most common, red is rarest
        
        // Weighted random selection
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Phaser.Math.Between(1, totalWeight);
        
        let selectedColor = 'pink';
        for (let i = 0; i < colors.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                selectedColor = colors[i];
                break;
            }
        }
        
        const x = Phaser.Math.Between(50, worldBounds.width - 50);
        const y = Phaser.Math.Between(50, worldBounds.height - 50);
        
        return new Food(scene, x, y, {
            color: selectedColor,
            size: Food.getSizeForColor(selectedColor),
            value: Food.getValueForColor(selectedColor)
        });
    }

    static createFoodFromData(scene, foodData) {
        return new Food(scene, foodData.x, foodData.y, {
            id: foodData.id,
            color: foodData.color,
            size: foodData.size,
            value: Food.getValueForColor(foodData.color)
        });
    }

    // Cleanup
    destroy() {
        // Stop all tweens
        this.scene.tweens.killTweensOf(this);
        
        // Remove glow sprite
        if (this.glowSprite) {
            this.scene.tweens.killTweensOf(this.glowSprite);
            this.glowSprite.destroy();
        }
        
        // Call parent destroy
        super.destroy();
    }

    // Getters
    getId() {
        return this.foodConfig.id;
    }

    getColor() {
        return this.foodConfig.color;
    }

    getSize() {
        return this.foodConfig.size;
    }

    getValue() {
        return this.foodConfig.value;
    }

    getData() {
        return {
            id: this.foodConfig.id,
            x: this.x,
            y: this.y,
            color: this.foodConfig.color,
            size: this.foodConfig.size,
            value: this.foodConfig.value
        };
    }
}
