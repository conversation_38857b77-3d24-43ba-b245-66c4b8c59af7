import Phaser from 'phaser';

export default class BootState extends Phaser.Scene {
    constructor() {
        super({ key: 'BootState' });
    }

    preload() {
        console.log('BootState: Loading assets...');
        
        // Create loading bar
        this.createLoadingBar();
        
        // Load snake assets (we'll create these programmatically for now)
        this.createSnakeAssets();
        
        // Load food assets
        this.createFoodAssets();
        
        // Load UI assets
        this.createUIAssets();
        
        // Set up loading events
        this.load.on('progress', this.updateLoadingBar, this);
        this.load.on('complete', this.loadComplete, this);
    }

    createLoadingBar() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Loading background
        this.add.rectangle(width / 2, height / 2, 400, 50, 0x222222);
        
        // Loading bar
        this.loadingBar = this.add.rectangle(width / 2 - 190, height / 2, 0, 30, 0x00ff88);
        this.loadingBar.setOrigin(0, 0.5);
        
        // Loading text
        this.loadingText = this.add.text(width / 2, height / 2 - 50, 'Loading...', {
            fontSize: '24px',
            fontFamily: 'Orbitron',
            fill: '#00ff88'
        }).setOrigin(0.5);
    }

    updateLoadingBar(progress) {
        this.loadingBar.width = 380 * progress;
        this.loadingText.setText(`Loading... ${Math.round(progress * 100)}%`);
    }

    loadComplete() {
        console.log('BootState: Assets loaded successfully');
        
        // Fade out loading screen
        this.cameras.main.fadeOut(500, 0, 0, 0);
        
        this.cameras.main.once('camerafadeoutcomplete', () => {
            this.scene.start('GameState');
        });
    }

    createSnakeAssets() {
        // Create snake head texture
        const headGraphics = this.add.graphics();
        headGraphics.fillStyle(0x00ff88);
        headGraphics.fillCircle(15, 15, 15);
        
        // Add eyes
        headGraphics.fillStyle(0xffffff);
        headGraphics.fillCircle(10, 10, 3);
        headGraphics.fillCircle(20, 10, 3);
        headGraphics.fillStyle(0x000000);
        headGraphics.fillCircle(10, 10, 2);
        headGraphics.fillCircle(20, 10, 2);
        
        headGraphics.generateTexture('snakeHead', 30, 30);
        headGraphics.destroy();

        // Create snake body texture
        const bodyGraphics = this.add.graphics();
        bodyGraphics.fillStyle(0x00cc77);
        bodyGraphics.fillCircle(12, 12, 12);
        
        // Add highlight
        bodyGraphics.fillStyle(0x00ff88);
        bodyGraphics.fillCircle(8, 8, 4);
        
        bodyGraphics.generateTexture('snakeBody', 24, 24);
        bodyGraphics.destroy();

        // Create enemy snake head texture
        const enemyHeadGraphics = this.add.graphics();
        enemyHeadGraphics.fillStyle(0xff6b6b);
        enemyHeadGraphics.fillCircle(15, 15, 15);
        
        // Add eyes
        enemyHeadGraphics.fillStyle(0xffffff);
        enemyHeadGraphics.fillCircle(10, 10, 3);
        enemyHeadGraphics.fillCircle(20, 10, 3);
        enemyHeadGraphics.fillStyle(0x000000);
        enemyHeadGraphics.fillCircle(10, 10, 2);
        enemyHeadGraphics.fillCircle(20, 10, 2);
        
        enemyHeadGraphics.generateTexture('enemySnakeHead', 30, 30);
        enemyHeadGraphics.destroy();

        // Create enemy snake body texture
        const enemyBodyGraphics = this.add.graphics();
        enemyBodyGraphics.fillStyle(0xcc5555);
        enemyBodyGraphics.fillCircle(12, 12, 12);
        
        // Add highlight
        enemyBodyGraphics.fillStyle(0xff6b6b);
        enemyBodyGraphics.fillCircle(8, 8, 4);
        
        enemyBodyGraphics.generateTexture('enemySnakeBody', 24, 24);
        enemyBodyGraphics.destroy();
    }

    createFoodAssets() {
        // Create different food types
        const foodColors = [
            { name: 'pink', color: 0xff6b6b },
            { name: 'blue', color: 0x4ecdc4 },
            { name: 'green', color: 0x45b7d1 },
            { name: 'lime', color: 0x96ceb4 },
            { name: 'red', color: 0xffeaa7 }
        ];

        foodColors.forEach(food => {
            const foodGraphics = this.add.graphics();
            
            // Main food circle
            foodGraphics.fillStyle(food.color);
            foodGraphics.fillCircle(8, 8, 8);
            
            // Highlight
            foodGraphics.fillStyle(0xffffff, 0.6);
            foodGraphics.fillCircle(6, 6, 3);
            
            foodGraphics.generateTexture(`food_${food.name}`, 16, 16);
            foodGraphics.destroy();
        });
    }

    createUIAssets() {
        // Create minimap background
        const minimapGraphics = this.add.graphics();
        minimapGraphics.fillStyle(0x000000, 0.8);
        minimapGraphics.fillCircle(75, 75, 75);
        minimapGraphics.lineStyle(3, 0x00ff88);
        minimapGraphics.strokeCircle(75, 75, 75);
        
        minimapGraphics.generateTexture('minimapBg', 150, 150);
        minimapGraphics.destroy();

        // Create particle textures for Phase 3 effects
        this.createParticleTextures();
    }

    createParticleTextures() {
        // Basic particle
        const particleGraphics = this.add.graphics();
        particleGraphics.fillStyle(0xffffff);
        particleGraphics.fillCircle(2, 2, 2);
        particleGraphics.generateTexture('particle', 4, 4);
        particleGraphics.destroy();

        // Glow particle
        const glowGraphics = this.add.graphics();
        glowGraphics.fillStyle(0xffffff, 0.8);
        glowGraphics.fillCircle(3, 3, 3);
        glowGraphics.fillStyle(0xffffff, 0.3);
        glowGraphics.fillCircle(3, 3, 6);
        glowGraphics.generateTexture('particle_glow', 6, 6);
        glowGraphics.destroy();

        // Star particle
        const starGraphics = this.add.graphics();
        starGraphics.fillStyle(0xffff00);
        const points = [];
        for (let i = 0; i < 10; i++) {
            const angle = (i * Math.PI) / 5;
            const radius = i % 2 === 0 ? 4 : 2;
            points.push(4 + Math.cos(angle) * radius);
            points.push(4 + Math.sin(angle) * radius);
        }
        starGraphics.fillPoints(points, true);
        starGraphics.generateTexture('particle_star', 8, 8);
        starGraphics.destroy();

        // Spark particle
        const sparkGraphics = this.add.graphics();
        sparkGraphics.lineStyle(1, 0xff6b6b);
        sparkGraphics.moveTo(0, 2);
        sparkGraphics.lineTo(4, 2);
        sparkGraphics.moveTo(2, 0);
        sparkGraphics.lineTo(2, 4);
        sparkGraphics.generateTexture('particle_spark', 4, 4);
        sparkGraphics.destroy();

        // Trail particle
        const trailGraphics = this.add.graphics();
        trailGraphics.fillStyle(0x00ccff, 0.6);
        trailGraphics.fillCircle(2, 2, 2);
        trailGraphics.generateTexture('particle_trail', 4, 4);
        trailGraphics.destroy();
    }
}
