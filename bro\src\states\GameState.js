import Phaser from 'phaser';
import Snake from '../objects/Snake';
import Food from '../objects/Food';
import NetworkManager from '../managers/NetworkManager';
import InputManager from '../managers/InputManager';
import CollisionManager from '../managers/CollisionManager';
import FoodManager from '../managers/FoodManager';

export default class GameState extends Phaser.Scene {
    constructor() {
        super({ key: 'GameState' });
    }

    init() {
        console.log('GameState: Initializing...');
        
        // Game objects
        this.mySnake = null;
        this.otherSnakes = [];

        // Managers
        this.networkManager = null;
        this.inputManager = null;
        this.collisionManager = null;
        this.foodManager = null;
        
        // Game state
        this.gameStarted = false;
        this.playerId = null;
        
        // Performance tracking
        this.lastUpdateTime = 0;
        this.frameCount = 0;
    }

    create() {
        console.log('GameState: Creating game world...');
        
        // Set world bounds
        this.physics.world.setBounds(0, 0, this.game.gameConfig.WORLD_WIDTH, this.game.gameConfig.WORLD_HEIGHT);
        
        // Create background
        this.createBackground();
        
        // Create groups for game objects
        this.createGroups();
        
        // Initialize managers
        this.initializeManagers();
        
        // Set up camera
        this.setupCamera();
        
        // Start UI state as overlay
        this.scene.launch('UIState');
        
        // Listen for game start
        this.events.on('startGame', this.startGame, this);
        
        console.log('GameState: Ready for players');
    }

    createBackground() {
        // Create a tiled background pattern
        const graphics = this.add.graphics();
        
        // Grid pattern
        graphics.lineStyle(1, 0x00ff88, 0.1);
        
        const gridSize = 50;
        const worldWidth = this.game.gameConfig.WORLD_WIDTH;
        const worldHeight = this.game.gameConfig.WORLD_HEIGHT;
        
        // Vertical lines
        for (let x = 0; x <= worldWidth; x += gridSize) {
            graphics.moveTo(x, 0);
            graphics.lineTo(x, worldHeight);
        }
        
        // Horizontal lines
        for (let y = 0; y <= worldHeight; y += gridSize) {
            graphics.moveTo(0, y);
            graphics.lineTo(worldWidth, y);
        }
        
        graphics.strokePath();
        
        // Create world border
        graphics.lineStyle(8, 0xff0000, 1);
        graphics.strokeRect(0, 0, worldWidth, worldHeight);
    }

    createGroups() {
        // Create physics groups
        this.snakeGroup = this.physics.add.group();
        this.particleGroup = this.add.group();

        console.log('GameState: Groups created');
    }

    initializeManagers() {
        // Initialize collision manager
        this.collisionManager = new CollisionManager(this);

        // Initialize food manager
        this.foodManager = new FoodManager(this);

        // Initialize network manager
        this.networkManager = new NetworkManager(this);

        // Initialize input manager
        this.inputManager = new InputManager(this);

        console.log('GameState: Managers initialized');
    }

    setupCamera() {
        // Camera will follow the player snake when created
        this.cameras.main.setBounds(0, 0, this.game.gameConfig.WORLD_WIDTH, this.game.gameConfig.WORLD_HEIGHT);
        
        // Smooth camera following
        this.cameras.main.setLerp(0.1, 0.1);
        
        console.log('GameState: Camera configured');
    }

    startGame(playerData) {
        console.log('GameState: Starting game for player:', playerData.nickname);
        
        this.gameStarted = true;
        this.playerId = playerData.playerId;
        
        // Create player snake
        this.createPlayerSnake(playerData);
        
        // Enable managers
        this.collisionManager.enable();
        this.foodManager.enable();
        this.inputManager.enable();

        // Connect to network
        this.networkManager.connect(playerData);

        console.log('GameState: Game started successfully');
    }

    createPlayerSnake(playerData) {
        const startX = this.game.gameConfig.WORLD_WIDTH / 2;
        const startY = this.game.gameConfig.WORLD_HEIGHT / 2;
        
        this.mySnake = new Snake(this, startX, startY, {
            isPlayer: true,
            nickname: playerData.nickname,
            color: 0x00ff88,
            headTexture: 'snakeHead',
            bodyTexture: 'snakeBody'
        });
        
        // Add to collision system
        this.collisionManager.addSnakeHead(this.mySnake.head);
        this.mySnake.bodySegments.forEach(segment => {
            this.collisionManager.addSnakeBody(segment);
        });

        // Add to physics group
        this.snakeGroup.add(this.mySnake.head);
        this.mySnake.bodySegments.forEach(segment => {
            this.snakeGroup.add(segment);
        });

        // Make camera follow player
        this.cameras.main.startFollow(this.mySnake.head);

        console.log('GameState: Player snake created');
    }

    createEnemySnake(snakeData) {
        const enemySnake = new Snake(this, snakeData.x, snakeData.y, {
            isPlayer: false,
            nickname: snakeData.nickname,
            color: snakeData.color || 0xff6b6b,
            headTexture: 'enemySnakeHead',
            bodyTexture: 'enemySnakeBody',
            playerId: snakeData.id
        });
        
        this.otherSnakes.push(enemySnake);

        // Add to collision system
        this.collisionManager.addSnakeHead(enemySnake.head);
        enemySnake.bodySegments.forEach(segment => {
            this.collisionManager.addSnakeBody(segment);
        });

        // Add to physics group
        this.snakeGroup.add(enemySnake.head);
        enemySnake.bodySegments.forEach(segment => {
            this.snakeGroup.add(segment);
        });

        return enemySnake;
    }

    update(time, delta) {
        if (!this.gameStarted) return;
        
        // Update player snake
        if (this.mySnake) {
            this.mySnake.update(time, delta);
        }
        
        // Update other snakes
        this.otherSnakes.forEach(snake => {
            if (snake.active) {
                snake.update(time, delta);
            }
        });
        
        // Update managers
        if (this.inputManager) {
            this.inputManager.update(time, delta);
        }

        if (this.networkManager) {
            this.networkManager.update(time, delta);
        }

        if (this.foodManager) {
            this.foodManager.update(time, delta);
        }
        
        // Performance monitoring
        this.updatePerformanceStats(time);
    }

    updatePerformanceStats(time) {
        this.frameCount++;
        
        if (time - this.lastUpdateTime >= 1000) {
            const fps = this.frameCount;
            this.frameCount = 0;
            this.lastUpdateTime = time;
            
            // Emit FPS to UI
            this.events.emit('fpsUpdate', fps);
        }
    }

    // Network event handlers
    onPlayerJoined(playerData) {
        console.log('GameState: Player joined:', playerData.nickname);
        this.createEnemySnake(playerData);
    }

    onPlayerLeft(playerId) {
        console.log('GameState: Player left:', playerId);
        
        const snakeIndex = this.otherSnakes.findIndex(snake => snake.playerId === playerId);
        if (snakeIndex !== -1) {
            this.otherSnakes[snakeIndex].destroy();
            this.otherSnakes.splice(snakeIndex, 1);
        }
    }

    onGameStateUpdate(gameState) {
        // Update other players' positions
        gameState.players.forEach(playerData => {
            if (playerData.id === this.playerId) return;
            
            const snake = this.otherSnakes.find(s => s.playerId === playerData.id);
            if (snake) {
                snake.updateFromNetwork(playerData);
            } else {
                this.createEnemySnake(playerData);
            }
        });
        
        // Update food
        if (gameState.food && this.foodManager) {
            this.foodManager.updateFromNetwork(gameState.food);
        }
    }

    shutdown() {
        console.log('GameState: Shutting down...');
        
        // Clean up managers
        if (this.networkManager) {
            this.networkManager.disconnect();
        }

        if (this.inputManager) {
            this.inputManager.disable();
        }

        if (this.collisionManager) {
            this.collisionManager.disable();
        }

        if (this.foodManager) {
            this.foodManager.disable();
        }
        
        // Clean up game objects
        this.otherSnakes.forEach(snake => snake.destroy());
        this.otherSnakes = [];
        
        if (this.mySnake) {
            this.mySnake.destroy();
            this.mySnake = null;
        }
    }
}
