import Phaser from 'phaser';

export default class Snake {
    constructor(scene, x, y, config = {}) {
        this.scene = scene;
        this.config = {
            isPlayer: false,
            nickname: 'Anonymous',
            color: 0x00ff88,
            headTexture: 'snakeHead',
            bodyTexture: 'snakeBody',
            playerId: null,
            ...config
        };
        
        // Snake properties
        this.active = true;
        this.alive = true;
        this.length = 5;
        this.speed = this.scene.game.gameConfig.SNAKE_SPEED;
        this.boostSpeed = this.scene.game.gameConfig.BOOST_SPEED;
        this.currentSpeed = this.speed;
        this.isBoosting = false;
        
        // Movement properties (like original)
        this.snakePath = [];
        this.pathLength = 200; // Store more path points for smoother following
        this.segmentSpacing = this.scene.game.gameConfig.SEGMENT_SPACING;
        
        // Create snake head
        this.createHead(x, y);
        
        // Create initial body segments
        this.bodySegments = [];
        this.createInitialBody();
        
        // Initialize path
        this.initializePath();
        
        // Input properties (for player snake)
        if (this.config.isPlayer) {
            this.targetAngle = 0;
            this.turnSpeed = this.scene.game.gameConfig.TURN_SPEED;
        }
        
        console.log(`Snake created: ${this.config.nickname} (Player: ${this.config.isPlayer})`);
    }

    createHead(x, y) {
        this.head = this.scene.physics.add.sprite(x, y, this.config.headTexture);
        this.head.setOrigin(0.5, 0.5);
        this.head.setCollideWorldBounds(true);
        
        // Set initial rotation
        this.head.rotation = 0;
        
        // Add physics properties
        this.head.body.setCircle(15); // Collision circle
        this.head.body.setDrag(0); // No drag for smooth movement
        
        // Add snake reference to head
        this.head.snake = this;
        
        // Add nickname text
        if (this.config.nickname) {
            this.nicknameText = this.scene.add.text(x, y - 40, this.config.nickname, {
                fontSize: '14px',
                fontFamily: 'Arial',
                fill: '#ffffff',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);
        }
    }

    createInitialBody() {
        for (let i = 0; i < this.length; i++) {
            const segment = this.scene.physics.add.sprite(
                this.head.x - (i + 1) * this.segmentSpacing,
                this.head.y,
                this.config.bodyTexture
            );
            
            segment.setOrigin(0.5, 0.5);
            segment.body.setCircle(12);
            segment.body.setImmovable(true);
            segment.snake = this;
            
            this.bodySegments.push(segment);
        }
    }

    initializePath() {
        // Initialize path with current head position (like original)
        for (let i = 0; i < this.pathLength; i++) {
            this.snakePath.push(new Phaser.Math.Vector2(this.head.x, this.head.y));
        }
    }

    update(time, delta) {
        if (!this.alive || !this.active) return;
        
        if (this.config.isPlayer) {
            this.updatePlayerMovement(time, delta);
        }
        
        this.updateMovement(time, delta);
        this.updateBodySegments();
        this.updateNickname();
    }

    updatePlayerMovement(time, delta) {
        // Get input from scene's input manager
        const input = this.scene.inputManager?.getInput();
        if (!input) return;
        
        // Calculate target angle based on mouse/touch position
        const worldPointer = this.scene.cameras.main.getWorldPoint(input.x, input.y);
        this.targetAngle = Phaser.Math.Angle.Between(
            this.head.x, this.head.y,
            worldPointer.x, worldPointer.y
        );
        
        // Smooth rotation (like original)
        let angleDiff = this.targetAngle - this.head.rotation;
        
        // Normalize angle difference
        while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
        while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
        
        // Apply smooth turning
        if (Math.abs(angleDiff) > 0.1) {
            const rotationSpeed = this.turnSpeed * (delta / 1000);
            this.head.rotation += Math.sign(angleDiff) * Math.min(Math.abs(angleDiff), rotationSpeed);
        }
        
        // Handle boost
        this.isBoosting = input.boost && this.length > 5;
        this.currentSpeed = this.isBoosting ? this.boostSpeed : this.speed;
        
        // Drain length when boosting
        if (this.isBoosting && this.length > 5) {
            this.length = Math.max(5, this.length - 0.8 * (delta / 1000));
        }
    }

    updateMovement(time, delta) {
        // Move head forward (like original physics-based movement)
        const velocity = this.scene.physics.velocityFromAngle(
            Phaser.Math.RadToDeg(this.head.rotation),
            this.currentSpeed
        );
        
        this.head.body.setVelocity(velocity.x, velocity.y);
        
        // Record head position in path (like original)
        const pathPoint = this.snakePath.pop();
        pathPoint.set(this.head.x, this.head.y);
        this.snakePath.unshift(pathPoint);
    }

    updateBodySegments() {
        // Update body segments to follow path (like original)
        for (let i = 0; i < this.bodySegments.length && i < this.length - 1; i++) {
            const pathIndex = (i + 1) * this.segmentSpacing;
            
            if (pathIndex < this.snakePath.length) {
                const targetPos = this.snakePath[pathIndex];
                this.bodySegments[i].x = targetPos.x;
                this.bodySegments[i].y = targetPos.y;
                this.bodySegments[i].setVisible(true);
            } else {
                this.bodySegments[i].setVisible(false);
            }
        }
        
        // Hide excess segments
        for (let i = Math.floor(this.length - 1); i < this.bodySegments.length; i++) {
            this.bodySegments[i].setVisible(false);
        }
    }

    updateNickname() {
        if (this.nicknameText) {
            this.nicknameText.x = this.head.x;
            this.nicknameText.y = this.head.y - 40;
        }
    }

    grow(amount = 3) {
        this.length += amount;
        
        // Add more body segments if needed
        while (this.bodySegments.length < this.length) {
            const segment = this.scene.physics.add.sprite(
                this.head.x,
                this.head.y,
                this.config.bodyTexture
            );
            
            segment.setOrigin(0.5, 0.5);
            segment.body.setCircle(12);
            segment.body.setImmovable(true);
            segment.snake = this;
            segment.setVisible(false);
            
            this.bodySegments.push(segment);
            this.scene.snakeGroup.add(segment);
        }
    }

    updateFromNetwork(networkData) {
        // Update position from network (for other players)
        if (!this.config.isPlayer) {
            // Smooth interpolation to network position
            const targetX = networkData.x;
            const targetY = networkData.y;
            
            // Lerp to target position
            this.head.x = Phaser.Math.Linear(this.head.x, targetX, 0.2);
            this.head.y = Phaser.Math.Linear(this.head.y, targetY, 0.2);
            
            // Update rotation if provided
            if (networkData.angle !== undefined) {
                this.head.rotation = networkData.angle;
            }
            
            // Update length
            if (networkData.length !== undefined) {
                this.length = networkData.length;
            }
        }
    }

    getNetworkData() {
        // Return data to send over network
        return {
            id: this.config.playerId,
            x: this.head.x,
            y: this.head.y,
            angle: this.head.rotation,
            length: this.length,
            nickname: this.config.nickname,
            alive: this.alive
        };
    }

    destroy() {
        console.log(`Destroying snake: ${this.config.nickname}`);
        
        this.active = false;
        this.alive = false;
        
        // Destroy head
        if (this.head) {
            this.head.destroy();
        }
        
        // Destroy body segments
        this.bodySegments.forEach(segment => {
            segment.destroy();
        });
        this.bodySegments = [];
        
        // Destroy nickname text
        if (this.nicknameText) {
            this.nicknameText.destroy();
        }
        
        // Clear path
        this.snakePath = [];
    }

    kill() {
        this.alive = false;
        
        // Add death effect
        this.createDeathEffect();
        
        // Hide snake
        this.head.setVisible(false);
        this.bodySegments.forEach(segment => segment.setVisible(false));
        
        if (this.nicknameText) {
            this.nicknameText.setVisible(false);
        }
    }

    createDeathEffect() {
        // Create particle explosion effect
        const particles = this.scene.add.particles(this.head.x, this.head.y, 'particle', {
            speed: { min: 50, max: 150 },
            scale: { start: 0.5, end: 0 },
            lifespan: 1000,
            quantity: 20
        });
        
        // Remove particles after animation
        this.scene.time.delayedCall(1000, () => {
            particles.destroy();
        });
    }
}
