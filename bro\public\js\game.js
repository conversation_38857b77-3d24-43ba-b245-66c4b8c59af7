// Game class for client-side game logic
class Game {
    constructor() {
        try {
            this.canvas = document.getElementById('gameCanvas');
            this.ctx = this.canvas.getContext('2d');
            this.minimap = document.getElementById('minimap');
            this.minimapCtx = this.minimap.getContext('2d');
            
            if (!this.canvas || !this.ctx) {
                throw new Error('Canvas not found or context failed');
            }
            
            this.players = [];
            this.food = [];
            this.myPlayer = null;
            this.camera = { x: 0, y: 0 };
            this.worldSize = { width: 4000, height: 4000 };
            this.keys = {};
            this.mouse = { x: 0, y: 0 };
            this.touch = { active: false, x: 0, y: 0 };
            this.isMobile = this.detectMobile();
            
            this.setupCanvas();
            this.setupEventListeners();
            this.startGameLoop();
            
        } catch (error) {
            console.error('Error initializing Game class:', error);
        }
    }

    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
               window.innerWidth <= 768 || 
               ('ontouchstart' in window);
    }

    setupCanvas() {
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        // Adjust minimap size for mobile
        if (this.isMobile) {
            this.minimap.width = 100;
            this.minimap.height = 100;
        } else {
            this.minimap.width = 150;
            this.minimap.height = 150;
        }
    }

    setupEventListeners() {
        // Mouse movement (for desktop)
        this.canvas.addEventListener('mousemove', (e) => {
            if (!this.isMobile) {
                this.mouse.x = e.clientX;
                this.mouse.y = e.clientY;
            }
        });

        // Mouse click for boost (desktop)
        this.canvas.addEventListener('mousedown', (e) => {
            if (!this.isMobile) {
                this.mouse.down = true;
                e.preventDefault();
            }
        });

        this.canvas.addEventListener('mouseup', (e) => {
            if (!this.isMobile) {
                this.mouse.down = false;
                e.preventDefault();
            }
        });

        // Touch events for mobile
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.touch.active = true;
            this.touch.down = true;
            const touch = e.touches[0];
            this.touch.x = touch.clientX;
            this.touch.y = touch.clientY;
            
            // Update mouse position for mobile compatibility
            this.mouse.x = touch.clientX;
            this.mouse.y = touch.clientY;
            this.mouse.down = true;
        });

        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (this.touch.active) {
                const touch = e.touches[0];
                this.touch.x = touch.clientX;
                this.touch.y = touch.clientY;
                
                // Update mouse position for mobile compatibility
                this.mouse.x = touch.clientX;
                this.mouse.y = touch.clientY;
            }
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.touch.active = false;
            this.touch.down = false;
            this.mouse.down = false;
        });

        this.canvas.addEventListener('touchcancel', (e) => {
            e.preventDefault();
            this.touch.active = false;
            this.touch.down = false;
            this.mouse.down = false;
        });

        // Prevent context menu on long touch
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // Keyboard events (for desktop)
        document.addEventListener('keydown', (e) => {
            this.keys[e.key.toLowerCase()] = true;
            
            // Space bar for boost
            if (e.key === ' ') {
                e.preventDefault();
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.key.toLowerCase()] = false;
        });

        // Prevent zoom on mobile
        document.addEventListener('gesturestart', (e) => {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', (e) => {
            e.preventDefault();
        });

        document.addEventListener('gestureend', (e) => {
            e.preventDefault();
        });
    }

    updateCamera() {
        if (this.myPlayer) {
            // More responsive camera following adjusted for slower snake speed
            const targetX = this.myPlayer.x - this.canvas.width / 2;
            const targetY = this.myPlayer.y - this.canvas.height / 2;
            
            // Adjusted camera smoothing for slower gameplay
            this.camera.x += (targetX - this.camera.x) * 0.1;
            this.camera.y += (targetY - this.camera.y) * 0.1;
            
            // Keep camera within world bounds
            this.camera.x = Math.max(0, Math.min(this.worldSize.width - this.canvas.width, this.camera.x));
            this.camera.y = Math.max(0, Math.min(this.worldSize.height - this.canvas.height, this.camera.y));
        }
    }

    render() {
        try {
            // Clear canvas
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            // Draw background grid (less detailed on mobile)
            this.drawGrid();
            
            // Draw game elements
            this.drawFood();
            this.drawSnakes();
            this.drawBorders();
            
            // Update minimap
            this.updateMinimap();
            
            // Draw mobile instructions if needed
            if (this.isMobile && this.players.length === 0) {
                this.drawMobileInstructions();
            }
            
        } catch (error) {
            console.error('Error in render:', error);
        }
    }

    drawMobileInstructions() {
        this.ctx.fillStyle = 'rgba(0, 255, 136, 0.8)';
        this.ctx.font = '20px Orbitron';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Touch to move your snake', this.canvas.width / 2, this.canvas.height / 2 - 50);
        this.ctx.fillText('Hold to boost', this.canvas.width / 2, this.canvas.height / 2 - 20);
    }

    drawGrid() {
        // Use larger grid size on mobile for better performance
        const gridSize = this.isMobile ? 40 : 25;
        this.ctx.strokeStyle = 'rgba(0, 255, 136, 0.05)';
        this.ctx.lineWidth = 1;
        
        const startX = Math.floor(this.camera.x / gridSize) * gridSize;
        const startY = Math.floor(this.camera.y / gridSize) * gridSize;
        
        // Draw fewer lines on mobile
        const stepSize = this.isMobile ? 2 : 1;
        
        for (let x = startX; x < this.camera.x + this.canvas.width; x += gridSize * stepSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x - this.camera.x, 0);
            this.ctx.lineTo(x - this.camera.x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = startY; y < this.camera.y + this.canvas.height; y += gridSize * stepSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y - this.camera.y);
            this.ctx.lineTo(this.canvas.width, y - this.camera.y);
            this.ctx.stroke();
        }
    }

    drawFood() {
        this.food.forEach(foodItem => {
            const x = foodItem.x - this.camera.x;
            const y = foodItem.y - this.camera.y;
            
            // Only draw if visible
            if (x >= -foodItem.size && x <= this.canvas.width + foodItem.size &&
                y >= -foodItem.size && y <= this.canvas.height + foodItem.size) {
                
                if (this.isMobile) {
                    // Simplified food rendering for mobile
                    this.ctx.fillStyle = foodItem.color;
                    this.ctx.beginPath();
                    this.ctx.arc(x, y, foodItem.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    
                    // Simple highlight
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    this.ctx.beginPath();
                    this.ctx.arc(x - 2, y - 2, foodItem.size * 0.4, 0, Math.PI * 2);
                    this.ctx.fill();
                } else {
                    // Full effects for desktop
                    this.ctx.shadowColor = foodItem.color;
                    this.ctx.shadowBlur = 12;
                    
                    const gradient = this.ctx.createRadialGradient(
                        x - 2, y - 2, 0,
                        x, y, foodItem.size
                    );
                    gradient.addColorStop(0, this.lightenColor(foodItem.color, 50));
                    gradient.addColorStop(0.7, foodItem.color);
                    gradient.addColorStop(1, this.darkenColor(foodItem.color, 20));
                    
                    this.ctx.fillStyle = gradient;
                    this.ctx.beginPath();
                    this.ctx.arc(x, y, foodItem.size, 0, Math.PI * 2);
                    this.ctx.fill();
                    
                    this.ctx.shadowBlur = 0;
                    this.ctx.strokeStyle = this.darkenColor(foodItem.color, 30);
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.arc(x, y, foodItem.size, 0, Math.PI * 2);
                    this.ctx.stroke();
                    
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.beginPath();
                    this.ctx.arc(x - 2, y - 2, foodItem.size * 0.3, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }
        });
    }

    lightenColor(color, percent) {
        // Handle both hex and CSS color formats
        if (color.startsWith('#')) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
        // Return original color if not hex
        return color;
    }

    drawSnakes() {
        if (this.players.length === 0) {
            return;
        }
        
        // Sort players by size (larger snakes drawn first, smaller on top)
        const sortedPlayers = this.players.sort((a, b) => {
            const aSize = a.segments ? a.segments.length : 0;
            const bSize = b.segments ? b.segments.length : 0;
            return bSize - aSize;
        });
        
        sortedPlayers.forEach((player, index) => {
            try {
                if (!player.alive) {
                    return;
                }
                
                // Ensure we have valid coordinates
                if (typeof player.x !== 'number' || typeof player.y !== 'number') {
                    return;
                }
                
                let segments = player.segments;
                if (!segments || segments.length === 0) {
                    segments = [{ x: player.x, y: player.y }];
                }
                
                this.drawSnakeBody(player, segments);
                this.drawSnakeHead(player, segments);
                
                // Draw nickname
                if (segments.length > 0) {
                    this.drawNickname(player, segments[0]);
                }
                
            } catch (error) {
                console.error(`Error drawing player ${index}:`, error);
            }
        });
    }

    drawSnakeBody(player, segments) {
        if (segments.length < 2) return;
        
        const baseSize = 12;
        const color = player.color || '#ff6b6b';
        
        // Draw body segments from tail to head
        for (let i = segments.length - 1; i >= 1; i--) {
            const segment = segments[i];
            const screenX = segment.x - this.camera.x;
            const screenY = segment.y - this.camera.y;
            
            // Only draw if on screen
            if (screenX < -50 || screenX > this.canvas.width + 50 || 
                screenY < -50 || screenY > this.canvas.height + 50) {
                continue;
            }
            
            // Calculate size based on position (head is largest)
            const sizeMultiplier = 1 - (i / segments.length) * 0.3;
            const segmentSize = baseSize * sizeMultiplier;
            
            // Draw main body with gradient
            const gradient = this.ctx.createRadialGradient(
                screenX - 3, screenY - 3, 0,
                screenX, screenY, segmentSize
            );
            gradient.addColorStop(0, this.lightenColor(color, 30));
            gradient.addColorStop(0.7, color);
            gradient.addColorStop(1, this.darkenColor(color, 20));
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.arc(screenX, screenY, segmentSize, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Draw darker outline
            this.ctx.strokeStyle = this.darkenColor(color, 40);
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.arc(screenX, screenY, segmentSize, 0, Math.PI * 2);
            this.ctx.stroke();
            
            // Draw inner highlight
            this.ctx.fillStyle = this.lightenColor(color, 50);
            this.ctx.beginPath();
            this.ctx.arc(screenX - 2, screenY - 2, segmentSize * 0.3, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawSnakeHead(player, segments) {
        if (segments.length === 0) return;
        
        const head = segments[0];
        const screenX = head.x - this.camera.x;
        const screenY = head.y - this.camera.y;
        
        // Only draw if on screen
        if (screenX < -50 || screenX > this.canvas.width + 50 || 
            screenY < -50 || screenY > this.canvas.height + 50) {
            return;
        }
        
        const headSize = 15;
        const color = player.color || '#ff6b6b';
        
        // Calculate head direction
        let angle = 0;
        if (segments.length > 1) {
            const neck = segments[1];
            angle = Math.atan2(head.y - neck.y, head.x - neck.x);
        }
        
        // Draw head with 3D gradient effect
        const headGradient = this.ctx.createRadialGradient(
            screenX - 4, screenY - 4, 0,
            screenX, screenY, headSize
        );
        headGradient.addColorStop(0, this.lightenColor(color, 40));
        headGradient.addColorStop(0.6, color);
        headGradient.addColorStop(1, this.darkenColor(color, 30));
        
        this.ctx.fillStyle = headGradient;
        this.ctx.beginPath();
        this.ctx.arc(screenX, screenY, headSize, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw head outline
        this.ctx.strokeStyle = this.darkenColor(color, 50);
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.arc(screenX, screenY, headSize, 0, Math.PI * 2);
        this.ctx.stroke();
        
        // Draw eyes
        this.drawSnakeEyes(screenX, screenY, angle, headSize);
        
        // Draw head highlight
        this.ctx.fillStyle = this.lightenColor(color, 60);
        this.ctx.beginPath();
        this.ctx.arc(screenX - 3, screenY - 3, headSize * 0.4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawSnakeEyes(x, y, angle, headSize) {
        // Simplified eyes for mobile performance
        if (this.isMobile) {
            const eyeDistance = headSize * 0.5;
            const eyeSize = 3;
            
            const eyeAngle1 = angle + Math.PI / 6;
            const eyeAngle2 = angle - Math.PI / 6;
            
            const eye1X = x + Math.cos(eyeAngle1) * eyeDistance;
            const eye1Y = y + Math.sin(eyeAngle1) * eyeDistance;
            const eye2X = x + Math.cos(eyeAngle2) * eyeDistance;
            const eye2Y = y + Math.sin(eyeAngle2) * eyeDistance;
            
            // Simple white eyes
            this.ctx.fillStyle = 'white';
            this.ctx.beginPath();
            this.ctx.arc(eye1X, eye1Y, eyeSize, 0, Math.PI * 2);
            this.ctx.arc(eye2X, eye2Y, eyeSize, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Simple black pupils
            this.ctx.fillStyle = 'black';
            this.ctx.beginPath();
            this.ctx.arc(eye1X, eye1Y, eyeSize * 0.6, 0, Math.PI * 2);
            this.ctx.arc(eye2X, eye2Y, eyeSize * 0.6, 0, Math.PI * 2);
            this.ctx.fill();
            
            return;
        }
        
        // Full detailed eyes for desktop
        const eyeDistance = headSize * 0.6;
        const eyeSize = 4;
        
        // Calculate eye positions
        const eyeAngle1 = angle + Math.PI / 6;
        const eyeAngle2 = angle - Math.PI / 6;
        
        const eye1X = x + Math.cos(eyeAngle1) * eyeDistance;
        const eye1Y = y + Math.sin(eyeAngle1) * eyeDistance;
        const eye2X = x + Math.cos(eyeAngle2) * eyeDistance;
        const eye2Y = y + Math.sin(eyeAngle2) * eyeDistance;
        
        // Draw eye sockets (darker)
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.beginPath();
        this.ctx.arc(eye1X, eye1Y, eyeSize + 1, 0, Math.PI * 2);
        this.ctx.arc(eye2X, eye2Y, eyeSize + 1, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw white part of eyes
        this.ctx.fillStyle = 'white';
        this.ctx.beginPath();
        this.ctx.arc(eye1X, eye1Y, eyeSize, 0, Math.PI * 2);
        this.ctx.arc(eye2X, eye2Y, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw pupils
        this.ctx.fillStyle = 'black';
        this.ctx.beginPath();
        this.ctx.arc(eye1X + 1, eye1Y + 1, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.arc(eye2X + 1, eye2Y + 1, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw eye highlights
        this.ctx.fillStyle = 'white';
        this.ctx.beginPath();
        this.ctx.arc(eye1X - 1, eye1Y - 1, eyeSize * 0.2, 0, Math.PI * 2);
        this.ctx.arc(eye2X - 1, eye2Y - 1, eyeSize * 0.2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawNickname(player, head) {
        const screenX = head.x - this.camera.x;
        const screenY = head.y - this.camera.y - 30;
        
        // Only draw if on screen
        if (screenX < -100 || screenX > this.canvas.width + 100 || 
            screenY < -50 || screenY > this.canvas.height + 50) {
            return;
        }
        
        this.ctx.font = 'bold 14px Arial';
        this.ctx.textAlign = 'center';
        
        // Draw text shadow
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillText(player.nickname || 'Unknown', screenX + 1, screenY + 1);
        
        // Draw text
        this.ctx.fillStyle = 'white';
        this.ctx.fillText(player.nickname || 'Unknown', screenX, screenY);
    }

    darkenColor(color, percent) {
        // Handle both hex and CSS color formats
        if (color.startsWith('#')) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) - amt;
            const G = (num >> 8 & 0x00FF) - amt;
            const B = (num & 0x0000FF) - amt;
            return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
                (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
                (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
        }
        // Return original color if not hex
        return color;
    }

    drawBorders() {
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 8;
        this.ctx.shadowColor = '#ff0000';
        this.ctx.shadowBlur = 10;
        
        // Top border
        if (this.camera.y <= 0) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, -this.camera.y);
            this.ctx.lineTo(this.canvas.width, -this.camera.y);
            this.ctx.stroke();
        }
        
        // Bottom border
        if (this.camera.y + this.canvas.height >= this.worldSize.height) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, this.worldSize.height - this.camera.y);
            this.ctx.lineTo(this.canvas.width, this.worldSize.height - this.camera.y);
            this.ctx.stroke();
        }
        
        // Left border
        if (this.camera.x <= 0) {
            this.ctx.beginPath();
            this.ctx.moveTo(-this.camera.x, 0);
            this.ctx.lineTo(-this.camera.x, this.canvas.height);
            this.ctx.stroke();
        }
        
        // Right border
        if (this.camera.x + this.canvas.width >= this.worldSize.width) {
            this.ctx.beginPath();
            this.ctx.moveTo(this.worldSize.width - this.camera.x, 0);
            this.ctx.lineTo(this.worldSize.width - this.camera.x, this.canvas.height);
            this.ctx.stroke();
        }
        
        this.ctx.shadowBlur = 0;
    }

    updateMinimap() {
        if (!this.myPlayer) return;
        
        const radius = this.minimap.width / 2;
        const centerX = radius;
        const centerY = radius;
        
        // Clear minimap
        this.minimapCtx.clearRect(0, 0, this.minimap.width, this.minimap.height);
        
        // Create circular clipping path
        this.minimapCtx.save();
        this.minimapCtx.beginPath();
        this.minimapCtx.arc(centerX, centerY, radius - 5, 0, Math.PI * 2);
        this.minimapCtx.clip();
        
        // Draw minimap background
        const gradient = this.minimapCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
        gradient.addColorStop(0, 'rgba(15, 15, 35, 0.8)');
        gradient.addColorStop(0.7, 'rgba(26, 26, 46, 0.9)');
        gradient.addColorStop(1, 'rgba(22, 33, 62, 0.95)');
        this.minimapCtx.fillStyle = gradient;
        this.minimapCtx.fillRect(0, 0, this.minimap.width, this.minimap.height);
        
        // Scale factors for circular minimap
        const scale = Math.min(this.minimap.width, this.minimap.height) / Math.max(this.worldSize.width, this.worldSize.height);
        const scaleX = scale;
        const scaleY = scale;
        
        // Calculate offset to center the world view
        const worldCenterX = this.worldSize.width / 2;
        const worldCenterY = this.worldSize.height / 2;
        const offsetX = centerX - (worldCenterX * scaleX);
        const offsetY = centerY - (worldCenterY * scaleY);
        
        // Draw world border
        this.minimapCtx.strokeStyle = 'rgba(255, 0, 0, 0.6)';
        this.minimapCtx.lineWidth = 2;
        this.minimapCtx.strokeRect(
            offsetX,
            offsetY,
            this.worldSize.width * scaleX,
            this.worldSize.height * scaleY
        );
        
        // Draw other players as small dots
        this.players.forEach(player => {
            if (!player.alive || player.id === this.myPlayer.id) return;
            
            const x = offsetX + (player.x * scaleX);
            const y = offsetY + (player.y * scaleY);
            
            // Only draw if within minimap circle
            const distFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
            if (distFromCenter <= radius - 5) {
                this.minimapCtx.fillStyle = player.color;
                this.minimapCtx.beginPath();
                this.minimapCtx.arc(x, y, 2, 0, Math.PI * 2);
                this.minimapCtx.fill();
                
                // Add small glow
                this.minimapCtx.shadowColor = player.color;
                this.minimapCtx.shadowBlur = 3;
                this.minimapCtx.beginPath();
                this.minimapCtx.arc(x, y, 1, 0, Math.PI * 2);
                this.minimapCtx.fill();
                this.minimapCtx.shadowBlur = 0;
            }
        });
        
        // Draw my player with special styling
        const myX = offsetX + (this.myPlayer.x * scaleX);
        const myY = offsetY + (this.myPlayer.y * scaleY);
        
        const myDistFromCenter = Math.sqrt((myX - centerX) ** 2 + (myY - centerY) ** 2);
        if (myDistFromCenter <= radius - 5) {
            // Draw my player with pulsing effect
            const pulseSize = 3 + Math.sin(Date.now() * 0.005) * 0.5;
            
            this.minimapCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.minimapCtx.beginPath();
            this.minimapCtx.arc(myX, myY, pulseSize, 0, Math.PI * 2);
            this.minimapCtx.fill();
            
            // Add bright glow
            this.minimapCtx.shadowColor = '#00ff88';
            this.minimapCtx.shadowBlur = 6;
            this.minimapCtx.fillStyle = '#00ff88';
            this.minimapCtx.beginPath();
            this.minimapCtx.arc(myX, myY, pulseSize - 1, 0, Math.PI * 2);
            this.minimapCtx.fill();
            this.minimapCtx.shadowBlur = 0;
        }
        
        // Draw camera viewport indicator
        const viewWidth = this.canvas.width * scaleX;
        const viewHeight = this.canvas.height * scaleY;
        const viewX = offsetX + (this.camera.x * scaleX);
        const viewY = offsetY + (this.camera.y * scaleY);
        
        this.minimapCtx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        this.minimapCtx.lineWidth = 1;
        this.minimapCtx.setLineDash([3, 3]);
        this.minimapCtx.strokeRect(viewX, viewY, viewWidth, viewHeight);
        this.minimapCtx.setLineDash([]);
        
        this.minimapCtx.restore();
        
        // Draw circular border
        this.minimapCtx.strokeStyle = '#00ff88';
        this.minimapCtx.lineWidth = 3;
        this.minimapCtx.beginPath();
        this.minimapCtx.arc(centerX, centerY, radius - 1.5, 0, Math.PI * 2);
        this.minimapCtx.stroke();
        
        // Draw inner decorative circle
        this.minimapCtx.strokeStyle = 'rgba(0, 255, 136, 0.3)';
        this.minimapCtx.lineWidth = 1;
        this.minimapCtx.beginPath();
        this.minimapCtx.arc(centerX, centerY, radius - 10, 0, Math.PI * 2);
        this.minimapCtx.stroke();
    }

    startGameLoop() {
        const gameLoop = () => {
            this.updateCamera();
            this.render();
            requestAnimationFrame(gameLoop);
        };
        
        gameLoop();
    }

    updateGameState(gameState) {
        this.players = gameState.players;
        this.food = gameState.food;
        
        // Find my player
        this.myPlayer = this.players.find(p => p.id === window.myPlayerId);
        
        // Debug logging
        console.log('Players in game state:', this.players.length);
        console.log('My player ID:', window.myPlayerId);
        console.log('My player found:', this.myPlayer ? 'Yes' : 'No');
        if (this.myPlayer) {
            console.log('My player position:', this.myPlayer.x, this.myPlayer.y);
            console.log('My player segments:', this.myPlayer.segments.length);
        }
        
        // Update HUD
        if (this.myPlayer) {
            document.getElementById('score').textContent = this.myPlayer.score;
            document.getElementById('length').textContent = this.myPlayer.length;
        }
    }

    getWorldMousePosition() {
        return {
            x: this.mouse.x + this.camera.x,
            y: this.mouse.y + this.camera.y
        };
    }
}

// Make Game class available globally
window.Game = Game;
