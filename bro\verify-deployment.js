#!/usr/bin/env node

const http = require('http');
const https = require('https');
const { execSync } = require('child_process');

console.log('🔍 Slither.io Deployment Verification');
console.log('=====================================');

// Configuration
const config = {
    host: process.env.HOST || 'localhost',
    port: process.env.PORT || 4000,
    domain: process.argv[2] || null, // Optional domain for external testing
    timeout: 5000
};

// Test functions
async function testLocalHealth() {
    console.log('\n📊 Testing local health endpoint...');
    
    return new Promise((resolve, reject) => {
        const req = http.request({
            hostname: config.host,
            port: config.port,
            path: '/health',
            method: 'GET',
            timeout: config.timeout
        }, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const health = JSON.parse(data);
                    console.log('✅ Health check passed');
                    console.log(`   Status: ${health.status}`);
                    console.log(`   Players: ${health.players}`);
                    console.log(`   Food: ${health.food}`);
                    console.log(`   Uptime: ${Math.round(health.uptime)}s`);
                    resolve(health);
                } catch (e) {
                    reject(new Error('Invalid health response'));
                }
            });
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Health check timeout')));
        req.end();
    });
}

async function testMainPage() {
    console.log('\n🌐 Testing main page...');
    
    return new Promise((resolve, reject) => {
        const req = http.request({
            hostname: config.host,
            port: config.port,
            path: '/',
            method: 'GET',
            timeout: config.timeout
        }, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ Main page accessible');
                console.log(`   Status: ${res.statusCode}`);
                console.log(`   Content-Type: ${res.headers['content-type']}`);
                resolve(true);
            } else {
                reject(new Error(`Main page returned ${res.statusCode}`));
            }
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Main page timeout')));
        req.end();
    });
}

async function testExternalAccess() {
    if (!config.domain) {
        console.log('\n🌍 Skipping external access test (no domain provided)');
        return;
    }
    
    console.log(`\n🌍 Testing external access to ${config.domain}...`);
    
    return new Promise((resolve, reject) => {
        const protocol = config.domain.startsWith('https://') ? https : http;
        const url = config.domain.startsWith('http') ? config.domain : `http://${config.domain}`;
        
        const req = protocol.request(url + '/health', {
            timeout: config.timeout
        }, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ External access working');
                console.log(`   Status: ${res.statusCode}`);
                resolve(true);
            } else {
                reject(new Error(`External access returned ${res.statusCode}`));
            }
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('External access timeout')));
        req.end();
    });
}

function checkPM2Status() {
    console.log('\n⚙️ Checking PM2 status...');
    
    try {
        const output = execSync('pm2 jlist', { encoding: 'utf8' });
        const processes = JSON.parse(output);
        
        const slitherProcess = processes.find(p => p.name === 'slither-game');
        
        if (slitherProcess) {
            console.log('✅ PM2 process found');
            console.log(`   Status: ${slitherProcess.pm2_env.status}`);
            console.log(`   PID: ${slitherProcess.pid}`);
            console.log(`   Memory: ${Math.round(slitherProcess.monit.memory / 1024 / 1024)}MB`);
            console.log(`   CPU: ${slitherProcess.monit.cpu}%`);
            console.log(`   Restarts: ${slitherProcess.pm2_env.restart_time}`);
            return true;
        } else {
            console.log('❌ PM2 process not found');
            return false;
        }
    } catch (error) {
        console.log('⚠️ PM2 not available or not running');
        return false;
    }
}

function checkNginxStatus() {
    console.log('\n🔧 Checking Nginx status...');
    
    try {
        const output = execSync('systemctl is-active nginx', { encoding: 'utf8' }).trim();
        
        if (output === 'active') {
            console.log('✅ Nginx is running');
            
            // Check if our site is enabled
            try {
                execSync('test -f /etc/nginx/sites-enabled/slither-game');
                console.log('✅ Slither-game site is enabled');
            } catch {
                console.log('⚠️ Slither-game site not found in nginx');
            }
            
            return true;
        } else {
            console.log(`❌ Nginx status: ${output}`);
            return false;
        }
    } catch (error) {
        console.log('⚠️ Cannot check Nginx status (may not be installed)');
        return false;
    }
}

function checkSystemResources() {
    console.log('\n💻 Checking system resources...');
    
    try {
        // Check memory
        const memInfo = execSync('free -m', { encoding: 'utf8' });
        const memLines = memInfo.split('\n');
        const memData = memLines[1].split(/\s+/);
        const totalMem = parseInt(memData[1]);
        const usedMem = parseInt(memData[2]);
        const memUsage = Math.round((usedMem / totalMem) * 100);
        
        console.log(`   Memory: ${usedMem}MB / ${totalMem}MB (${memUsage}%)`);
        
        if (memUsage > 90) {
            console.log('⚠️ High memory usage detected');
        } else {
            console.log('✅ Memory usage normal');
        }
        
        // Check disk space
        const diskInfo = execSync('df -h /', { encoding: 'utf8' });
        const diskLines = diskInfo.split('\n');
        const diskData = diskLines[1].split(/\s+/);
        const diskUsage = diskData[4];
        
        console.log(`   Disk usage: ${diskUsage}`);
        
        if (parseInt(diskUsage) > 90) {
            console.log('⚠️ High disk usage detected');
        } else {
            console.log('✅ Disk usage normal');
        }
        
        return true;
    } catch (error) {
        console.log('⚠️ Cannot check system resources');
        return false;
    }
}

function generateReport(results) {
    console.log('\n📋 Deployment Verification Report');
    console.log('==================================');
    
    const passed = results.filter(r => r.status === 'pass').length;
    const total = results.length;
    const score = Math.round((passed / total) * 100);
    
    console.log(`\n🎯 Overall Score: ${score}% (${passed}/${total} checks passed)\n`);
    
    results.forEach(result => {
        const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
        console.log(`${icon} ${result.test}: ${result.message}`);
    });
    
    console.log('\n📊 Recommendations:');
    
    if (score === 100) {
        console.log('🎉 Perfect! Your deployment is ready for production.');
    } else if (score >= 80) {
        console.log('👍 Good deployment. Address any warnings for optimal performance.');
    } else if (score >= 60) {
        console.log('⚠️ Deployment has issues. Please fix failed checks before going live.');
    } else {
        console.log('❌ Deployment has serious issues. Please review and fix all problems.');
    }
    
    console.log('\n🔗 Useful Commands:');
    console.log('   pm2 status          - Check PM2 processes');
    console.log('   pm2 logs slither-game - View application logs');
    console.log('   sudo systemctl status nginx - Check Nginx status');
    console.log('   sudo nginx -t       - Test Nginx configuration');
    console.log('   htop                - Monitor system resources');
}

// Main verification function
async function main() {
    const results = [];
    
    try {
        await testLocalHealth();
        results.push({ test: 'Health Endpoint', status: 'pass', message: 'Responding correctly' });
    } catch (error) {
        results.push({ test: 'Health Endpoint', status: 'fail', message: error.message });
    }
    
    try {
        await testMainPage();
        results.push({ test: 'Main Page', status: 'pass', message: 'Accessible' });
    } catch (error) {
        results.push({ test: 'Main Page', status: 'fail', message: error.message });
    }
    
    try {
        await testExternalAccess();
        results.push({ test: 'External Access', status: 'pass', message: 'Working' });
    } catch (error) {
        if (config.domain) {
            results.push({ test: 'External Access', status: 'fail', message: error.message });
        }
    }
    
    const pm2Status = checkPM2Status();
    results.push({ 
        test: 'PM2 Process', 
        status: pm2Status ? 'pass' : 'warn', 
        message: pm2Status ? 'Running' : 'Not found or not running' 
    });
    
    const nginxStatus = checkNginxStatus();
    results.push({ 
        test: 'Nginx Service', 
        status: nginxStatus ? 'pass' : 'warn', 
        message: nginxStatus ? 'Active' : 'Not running or not installed' 
    });
    
    const systemStatus = checkSystemResources();
    results.push({ 
        test: 'System Resources', 
        status: systemStatus ? 'pass' : 'warn', 
        message: systemStatus ? 'Normal' : 'Cannot check' 
    });
    
    generateReport(results);
}

// Run verification
main().catch(error => {
    console.error('\n❌ Verification failed:', error.message);
    process.exit(1);
});
