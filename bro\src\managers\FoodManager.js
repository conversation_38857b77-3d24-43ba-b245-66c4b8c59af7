import Food from '../objects/Food';

export default class FoodManager {
    constructor(scene) {
        this.scene = scene;
        this.enabled = false;
        
        // Food configuration
        this.config = {
            MAX_FOOD: 1000,
            SPAWN_RATE: 5, // Food items per second
            WORLD_PADDING: 50
        };
        
        // Food storage
        this.foodItems = new Map(); // id -> Food object
        this.foodGroup = null;
        
        // Spawning
        this.lastSpawnTime = 0;
        this.spawnInterval = 1000 / this.config.SPAWN_RATE; // 200ms between spawns
        
        // Performance optimization
        this.visibilityCheckInterval = 1000; // Check visibility every second
        this.lastVisibilityCheck = 0;
        
        console.log('FoodManager: Initialized');
    }

    enable() {
        if (this.enabled) return;
        
        this.enabled = true;
        this.setupFoodGroup();
        this.generateInitialFood();
        
        console.log('FoodManager: Enabled');
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.clearAllFood();
        
        console.log('FoodManager: Disabled');
    }

    setupFoodGroup() {
        // Create physics group for food
        this.foodGroup = this.scene.physics.add.group();
        
        // Add to collision manager if available
        if (this.scene.collisionManager) {
            this.scene.collisionManager.foodGroup = this.foodGroup;
        }
    }

    generateInitialFood() {
        console.log('FoodManager: Generating initial food...');
        
        const worldBounds = {
            width: this.scene.game.gameConfig.WORLD_WIDTH,
            height: this.scene.game.gameConfig.WORLD_HEIGHT
        };
        
        // Generate food in batches to avoid frame drops
        const batchSize = 50;
        let generated = 0;
        
        const generateBatch = () => {
            for (let i = 0; i < batchSize && generated < this.config.MAX_FOOD; i++) {
                this.spawnRandomFood(worldBounds);
                generated++;
            }
            
            if (generated < this.config.MAX_FOOD) {
                // Schedule next batch
                this.scene.time.delayedCall(50, generateBatch);
            } else {
                console.log(`FoodManager: Generated ${generated} food items`);
            }
        };
        
        generateBatch();
    }

    spawnRandomFood(worldBounds = null) {
        if (!worldBounds) {
            worldBounds = {
                width: this.scene.game.gameConfig.WORLD_WIDTH,
                height: this.scene.game.gameConfig.WORLD_HEIGHT
            };
        }
        
        const food = Food.createRandomFood(this.scene, worldBounds);
        this.addFood(food);
        
        return food;
    }

    spawnFoodAt(x, y, config = {}) {
        const food = new Food(this.scene, x, y, config);
        this.addFood(food);
        
        return food;
    }

    addFood(food) {
        // Store food reference
        this.foodItems.set(food.getId(), food);
        
        // Add to physics group
        if (this.foodGroup) {
            this.foodGroup.add(food);
        }
        
        // Add to collision manager
        if (this.scene.collisionManager) {
            this.scene.collisionManager.addFood(food);
        }
        
        // Listen for consumption
        food.on = food.on || (() => {}); // Fallback if events not available
        
        return food;
    }

    removeFood(foodId) {
        const food = this.foodItems.get(foodId);
        if (!food) return false;
        
        // Remove from storage
        this.foodItems.delete(foodId);
        
        // Remove from physics group
        if (this.foodGroup) {
            this.foodGroup.remove(food);
        }
        
        // Remove from collision manager
        if (this.scene.collisionManager) {
            this.scene.collisionManager.removeFood(food);
        }
        
        // Destroy the food object
        food.destroy();
        
        return true;
    }

    getFoodById(foodId) {
        return this.foodItems.get(foodId);
    }

    getAllFood() {
        return Array.from(this.foodItems.values());
    }

    getFoodCount() {
        return this.foodItems.size;
    }

    update(time, delta) {
        if (!this.enabled) return;
        
        // Spawn new food if needed
        this.updateFoodSpawning(time);
        
        // Update visibility for performance
        this.updateFoodVisibility(time);
        
        // Clean up destroyed food
        this.cleanupDestroyedFood();
    }

    updateFoodSpawning(time) {
        // Check if we need to spawn more food
        if (this.foodItems.size < this.config.MAX_FOOD && 
            time - this.lastSpawnTime >= this.spawnInterval) {
            
            this.spawnRandomFood();
            this.lastSpawnTime = time;
        }
    }

    updateFoodVisibility(time) {
        // Only check visibility periodically for performance
        if (time - this.lastVisibilityCheck < this.visibilityCheckInterval) return;
        
        const camera = this.scene.cameras.main;
        const viewBounds = {
            left: camera.scrollX - 100,
            right: camera.scrollX + camera.width + 100,
            top: camera.scrollY - 100,
            bottom: camera.scrollY + camera.height + 100
        };
        
        // Update visibility for all food items
        this.foodItems.forEach(food => {
            const visible = food.x >= viewBounds.left && 
                          food.x <= viewBounds.right &&
                          food.y >= viewBounds.top && 
                          food.y <= viewBounds.bottom;
            
            food.setVisible(visible);
            food.body.enable = visible; // Disable physics for invisible food
        });
        
        this.lastVisibilityCheck = time;
    }

    cleanupDestroyedFood() {
        // Remove destroyed food from our tracking
        const toRemove = [];
        
        this.foodItems.forEach((food, id) => {
            if (!food.active || food.scene === null) {
                toRemove.push(id);
            }
        });
        
        toRemove.forEach(id => {
            this.foodItems.delete(id);
        });
    }

    // Network synchronization
    updateFromNetwork(foodData) {
        // Clear existing food
        this.clearAllFood();
        
        // Add food from network data
        foodData.forEach(item => {
            const food = Food.createFoodFromData(this.scene, item);
            this.addFood(food);
        });
        
        console.log(`FoodManager: Updated from network (${foodData.length} items)`);
    }

    getNetworkData() {
        // Return food data for network synchronization
        return this.getAllFood().map(food => food.getData());
    }

    // Utility methods
    getFoodNear(x, y, radius) {
        const nearbyFood = [];
        
        this.foodItems.forEach(food => {
            const distance = Phaser.Math.Distance.Between(x, y, food.x, food.y);
            if (distance <= radius) {
                nearbyFood.push(food);
            }
        });
        
        return nearbyFood;
    }

    getFoodInArea(bounds) {
        const foodInArea = [];
        
        this.foodItems.forEach(food => {
            if (food.x >= bounds.left && food.x <= bounds.right &&
                food.y >= bounds.top && food.y <= bounds.bottom) {
                foodInArea.push(food);
            }
        });
        
        return foodInArea;
    }

    clearAllFood() {
        console.log('FoodManager: Clearing all food');
        
        // Destroy all food objects
        this.foodItems.forEach(food => {
            if (food.destroy) {
                food.destroy();
            }
        });
        
        // Clear storage
        this.foodItems.clear();
        
        // Clear physics group
        if (this.foodGroup) {
            this.foodGroup.clear(true, true);
        }
    }

    // Events
    onFoodConsumed(foodId, consumedBy) {
        console.log(`FoodManager: Food ${foodId} consumed by ${consumedBy}`);
        
        // Remove the consumed food
        this.removeFood(foodId);
        
        // Spawn replacement food
        this.spawnRandomFood();
        
        // Emit event
        this.scene.events.emit('foodConsumed', { foodId, consumedBy });
    }

    // Configuration
    setMaxFood(maxFood) {
        this.config.MAX_FOOD = maxFood;
    }

    setSpawnRate(spawnRate) {
        this.config.SPAWN_RATE = spawnRate;
        this.spawnInterval = 1000 / spawnRate;
    }

    // Cleanup
    destroy() {
        this.disable();
        this.clearAllFood();
        
        this.foodGroup = null;
        this.foodItems = null;
        
        console.log('FoodManager: Destroyed');
    }
}
