# 🚀 VPS Deployment Guide - Slither.io Game

## 📋 Prerequisites

### **VPS Requirements**
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **RAM**: Minimum 1GB (2GB+ recommended)
- **CPU**: 1 vCPU (2+ recommended)
- **Storage**: 10GB+ available space
- **Network**: Public IP address

### **Software Requirements**
- Node.js 18+
- npm 8+
- PM2 (process manager)
- <PERSON>inx (reverse proxy)
- Git

## 🛠️ Step 1: Server Setup

### **1.1 Connect to Your VPS**
```bash
ssh root@your-server-ip
# or
ssh your-username@your-server-ip
```

### **1.2 Update System**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### **1.3 Install Node.js**
```bash
# Install Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version  # Should show v18.x.x
npm --version   # Should show 8.x.x
```

### **1.4 Install PM2**
```bash
sudo npm install -g pm2
pm2 --version
```

### **1.5 Install Nginx**
```bash
# Ubuntu/Debian
sudo apt install nginx -y

# CentOS/RHEL
sudo yum install nginx -y

# Start and enable nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### **1.6 Install Git**
```bash
sudo apt install git -y
```

## 📦 Step 2: Deploy Application

### **2.1 Create Deploy User (Recommended)**
```bash
# Create deploy user
sudo adduser deploy
sudo usermod -aG sudo deploy

# Switch to deploy user
su - deploy
```

### **2.2 Clone Repository**
```bash
# Clone your game repository
git clone https://github.com/yourusername/slither-game.git
cd slither-game/bro

# Or upload files directly
mkdir -p /home/<USER>/slither-game
cd /home/<USER>/slither-game
# Upload your bro folder contents here
```

### **2.3 Install Dependencies**
```bash
npm install
```

### **2.4 Build Production Version**
```bash
npm run build
```

### **2.5 Configure Environment**
```bash
# Create environment file
cat > .env << EOF
NODE_ENV=production
PORT=4000
HOST=0.0.0.0
EOF
```

## 🔧 Step 3: Configure Services

### **3.1 Setup PM2**
```bash
# Start application with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u deploy --hp /home/<USER>

# Check status
pm2 status
pm2 logs slither-game
```

### **3.2 Configure Nginx**
```bash
# Copy nginx configuration
sudo cp nginx.conf /etc/nginx/sites-available/slither-game

# Enable site
sudo ln -s /etc/nginx/sites-available/slither-game /etc/nginx/sites-enabled/

# Remove default site (optional)
sudo rm /etc/nginx/sites-enabled/default

# Update domain in nginx config
sudo nano /etc/nginx/sites-available/slither-game
# Replace 'yourdomain.com' with your actual domain or IP

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

### **3.3 Configure Firewall**
```bash
# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

## 🔒 Step 4: SSL Setup (Optional but Recommended)

### **4.1 Install Certbot**
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### **4.2 Get SSL Certificate**
```bash
# Replace with your domain
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

## 🎯 Step 5: Verification

### **5.1 Check Services**
```bash
# Check PM2 status
pm2 status

# Check nginx status
sudo systemctl status nginx

# Check application logs
pm2 logs slither-game

# Check nginx logs
sudo tail -f /var/log/nginx/slither-game.access.log
sudo tail -f /var/log/nginx/slither-game.error.log
```

### **5.2 Test Application**
```bash
# Health check
curl http://localhost:4000/health

# Test from external
curl http://your-domain-or-ip/health
```

### **5.3 Performance Test**
```bash
# Check resource usage
pm2 monit

# System resources
htop
free -h
df -h
```

## 🔄 Step 6: Deployment Automation

### **6.1 Use Deployment Script**
```bash
# Make script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh production
```

### **6.2 Manual Deployment Commands**
```bash
# Update code
git pull origin main

# Install dependencies
npm ci --production

# Build application
npm run build

# Restart PM2
pm2 restart slither-game

# Reload nginx (if config changed)
sudo systemctl reload nginx
```

## 📊 Step 7: Monitoring & Maintenance

### **7.1 PM2 Monitoring**
```bash
# Real-time monitoring
pm2 monit

# View logs
pm2 logs slither-game

# Restart application
pm2 restart slither-game

# View detailed info
pm2 show slither-game
```

### **7.2 System Monitoring**
```bash
# Check disk space
df -h

# Check memory usage
free -h

# Check CPU usage
top

# Check network connections
netstat -tulpn | grep :4000
```

### **7.3 Log Management**
```bash
# Rotate PM2 logs
pm2 flush

# Check nginx logs
sudo tail -f /var/log/nginx/slither-game.access.log

# Archive old logs
sudo logrotate /etc/logrotate.d/nginx
```

## 🚨 Troubleshooting

### **Common Issues**

#### **Application Won't Start**
```bash
# Check PM2 logs
pm2 logs slither-game

# Check if port is in use
sudo netstat -tulpn | grep :4000

# Restart PM2
pm2 restart slither-game
```

#### **Nginx 502 Bad Gateway**
```bash
# Check if Node.js app is running
pm2 status

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Test nginx config
sudo nginx -t
```

#### **SSL Issues**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew

# Check nginx SSL config
sudo nginx -t
```

#### **Performance Issues**
```bash
# Check system resources
htop
free -h

# Check PM2 memory usage
pm2 monit

# Restart application
pm2 restart slither-game
```

## 🎉 Success!

Your Slither.io game should now be running on your VPS!

### **Access Your Game**
- **HTTP**: `http://your-domain-or-ip`
- **HTTPS**: `https://your-domain-or-ip` (if SSL configured)
- **Health Check**: `http://your-domain-or-ip/health`

### **Game Features Working**
- ✅ 60 FPS smooth gameplay
- ✅ Real-time multiplayer
- ✅ Mobile-optimized controls
- ✅ Particle effects and sounds
- ✅ Performance monitoring
- ✅ Auto-scaling and optimization

### **Production Features**
- ✅ PM2 process management
- ✅ Nginx reverse proxy
- ✅ SSL encryption
- ✅ Gzip compression
- ✅ Security headers
- ✅ Rate limiting
- ✅ Health monitoring

**🎮 Your production-ready snake game is live!** 🐍✨
