import io from 'socket.io-client';

export default class NetworkManager {
    constructor(scene) {
        this.scene = scene;
        this.socket = null;
        this.connected = false;
        this.playerId = null;
        
        // Network timing
        this.lastPositionUpdate = 0;
        this.lastGameStateUpdate = 0;
        this.positionUpdateRate = 1000 / this.scene.game.networkConfig.POSITION_UPDATE_RATE; // 50ms
        this.gameStateUpdateRate = 1000 / this.scene.game.networkConfig.GAME_STATE_RATE; // 100ms
        
        // Client prediction
        this.serverState = null;
        this.lastServerUpdate = 0;
        this.interpolationDelay = 100; // 100ms interpolation buffer
        
        console.log('NetworkManager: Initialized');
    }

    connect(playerData) {
        console.log('NetworkManager: Connecting to server...');
        
        this.socket = io();
        this.playerId = playerData.playerId;
        
        this.setupEventListeners();
        
        // Join game
        this.socket.emit('join-game', {
            nickname: playerData.nickname
        });
    }

    disconnect() {
        if (this.socket) {
            console.log('NetworkManager: Disconnecting...');
            this.socket.disconnect();
            this.socket = null;
            this.connected = false;
        }
    }

    setupEventListeners() {
        this.socket.on('connect', () => {
            console.log('NetworkManager: Connected to server');
            this.connected = true;
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('NetworkManager: Disconnected from server');
            this.connected = false;
            this.updateConnectionStatus(false);
        });

        this.socket.on('game-joined', (data) => {
            console.log('NetworkManager: Game joined successfully');
            this.playerId = data.playerId;
            
            // Update UI
            this.scene.events.emit('gameJoined', data);
        });

        this.socket.on('game-state', (gameState) => {
            this.handleGameStateUpdate(gameState);
        });

        this.socket.on('player-joined', (playerData) => {
            console.log('NetworkManager: Player joined:', playerData.nickname);
            this.scene.onPlayerJoined(playerData);
        });

        this.socket.on('player-left', (playerId) => {
            console.log('NetworkManager: Player left:', playerId);
            this.scene.onPlayerLeft(playerId);
        });

        this.socket.on('game-over', (data) => {
            console.log('NetworkManager: Game over');
            this.scene.events.emit('gameOver', data);
        });

        this.socket.on('leaderboard-update', (leaderboard) => {
            this.scene.events.emit('leaderboardUpdate', leaderboard);
        });

        this.socket.on('player-count', (count) => {
            this.scene.events.emit('playerCountUpdate', count);
        });

        this.socket.on('connect_error', (error) => {
            console.error('NetworkManager: Connection error:', error);
            this.updateConnectionStatus(false);
        });
    }

    update(time, delta) {
        if (!this.connected || !this.scene.mySnake) return;
        
        // Send position updates at reduced rate
        if (time - this.lastPositionUpdate >= this.positionUpdateRate) {
            this.sendPositionUpdate();
            this.lastPositionUpdate = time;
        }
        
        // Interpolate other players' positions
        this.interpolatePlayerPositions(time);
    }

    sendPositionUpdate() {
        if (!this.scene.mySnake || !this.connected) return;
        
        const snakeData = this.scene.mySnake.getNetworkData();
        const inputData = this.scene.inputManager?.getInput();
        
        // Send optimized data
        this.socket.emit('move', {
            x: Math.round(snakeData.x),
            y: Math.round(snakeData.y),
            angle: Math.round(snakeData.angle * 100) / 100, // Reduce precision
            boost: inputData?.boost || false,
            length: Math.round(snakeData.length * 10) / 10 // Reduce precision
        });
    }

    handleGameStateUpdate(gameState) {
        this.serverState = gameState;
        this.lastServerUpdate = Date.now();
        
        // Update scene with game state
        this.scene.onGameStateUpdate(gameState);
        
        // Reconcile player position if needed
        this.reconcilePlayerPosition(gameState);
    }

    reconcilePlayerPosition(gameState) {
        if (!this.scene.mySnake) return;
        
        // Find our player in the server state
        const serverPlayer = gameState.players.find(p => p.id === this.playerId);
        if (!serverPlayer) return;
        
        // Check if we're too far from server position
        const dx = this.scene.mySnake.head.x - serverPlayer.x;
        const dy = this.scene.mySnake.head.y - serverPlayer.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        // If distance is too large, smoothly correct position
        if (distance > 50) {
            console.log('NetworkManager: Correcting position, distance:', distance);
            
            // Smooth correction over time
            this.scene.tweens.add({
                targets: this.scene.mySnake.head,
                x: serverPlayer.x,
                y: serverPlayer.y,
                duration: 200,
                ease: 'Power2.easeOut'
            });
        }
    }

    interpolatePlayerPositions(time) {
        if (!this.serverState) return;
        
        const timeSinceUpdate = time - this.lastServerUpdate;
        const interpolationFactor = Math.min(timeSinceUpdate / this.interpolationDelay, 1);
        
        // Interpolate other players' positions for smooth movement
        this.scene.otherSnakes.forEach(snake => {
            if (!snake.active || snake.config.isPlayer) return;
            
            const serverPlayer = this.serverState.players.find(p => p.id === snake.config.playerId);
            if (!serverPlayer) return;
            
            // Smooth interpolation to server position
            const targetX = serverPlayer.x;
            const targetY = serverPlayer.y;
            
            snake.head.x = Phaser.Math.Linear(snake.head.x, targetX, 0.1);
            snake.head.y = Phaser.Math.Linear(snake.head.y, targetY, 0.1);
            
            // Update rotation
            if (serverPlayer.angle !== undefined) {
                snake.head.rotation = Phaser.Math.Angle.RotateTo(
                    snake.head.rotation,
                    serverPlayer.angle,
                    0.1
                );
            }
        });
    }

    updateConnectionStatus(connected) {
        this.connected = connected;
        this.scene.events.emit('connectionStatusChanged', connected);
    }

    // Utility methods
    isConnected() {
        return this.connected && this.socket && this.socket.connected;
    }

    getLatency() {
        return this.socket ? this.socket.ping : 0;
    }

    sendChatMessage(message) {
        if (this.isConnected()) {
            this.socket.emit('chat-message', {
                message: message,
                playerId: this.playerId
            });
        }
    }

    respawn(nickname) {
        if (this.isConnected()) {
            this.socket.emit('respawn', {
                nickname: nickname
            });
        }
    }

    // Event emitters for UI
    onFoodEaten(foodId) {
        if (this.isConnected()) {
            this.socket.emit('food-eaten', {
                foodId: foodId,
                playerId: this.playerId
            });
        }
    }

    onPlayerKilled(killedBy) {
        if (this.isConnected()) {
            this.socket.emit('player-killed', {
                playerId: this.playerId,
                killedBy: killedBy
            });
        }
    }

    // Performance monitoring
    getNetworkStats() {
        return {
            connected: this.connected,
            latency: this.getLatency(),
            positionUpdateRate: 1000 / this.positionUpdateRate,
            gameStateUpdateRate: 1000 / this.gameStateUpdateRate,
            lastUpdate: Date.now() - this.lastServerUpdate
        };
    }
}
