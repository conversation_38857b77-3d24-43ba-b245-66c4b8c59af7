export default class SoundManager {
    constructor(scene) {
        this.scene = scene;
        this.enabled = true;
        this.musicEnabled = true;
        this.sfxEnabled = true;
        
        // Volume settings
        this.masterVolume = 0.7;
        this.musicVolume = 0.4;
        this.sfxVolume = 0.6;
        
        // Audio objects
        this.sounds = new Map();
        this.music = null;
        
        // Audio pools for performance
        this.soundPools = new Map();
        
        console.log('SoundManager: Initialized');
    }

    init() {
        this.createAudioAssets();
        this.setupSoundPools();
        this.loadUserPreferences();
        
        console.log('SoundManager: Ready');
    }

    createAudioAssets() {
        // Create procedural audio using Web Audio API
        this.createEatSound();
        this.createBoostSound();
        this.createDeathSound();
        this.createBackgroundMusic();
        this.createUISound();
    }

    createEatSound() {
        // Create a pleasant "pop" sound for eating food
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        const duration = 0.2;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 800 + Math.sin(t * 20) * 200; // Wobble effect
            const envelope = Math.exp(-t * 8); // Quick decay
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3;
        }
        
        this.registerSound('eat', buffer);
    }

    createBoostSound() {
        // Create a "whoosh" sound for boosting
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        const duration = 0.3;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 200 + t * 400; // Rising frequency
            const envelope = Math.exp(-t * 3);
            const noise = (Math.random() - 0.5) * 0.1; // Add some noise
            data[i] = (Math.sin(2 * Math.PI * frequency * t) + noise) * envelope * 0.2;
        }
        
        this.registerSound('boost', buffer);
    }

    createDeathSound() {
        // Create a dramatic death sound
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        const duration = 1.0;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 400 - t * 300; // Falling frequency
            const envelope = Math.exp(-t * 2);
            const distortion = Math.sin(t * 50) * 0.3; // Add distortion
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * (0.4 + distortion);
        }
        
        this.registerSound('death', buffer);
    }

    createUISound() {
        // Create UI interaction sounds
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        // Click sound
        const clickDuration = 0.1;
        const clickBuffer = audioContext.createBuffer(1, clickDuration * audioContext.sampleRate, audioContext.sampleRate);
        const clickData = clickBuffer.getChannelData(0);
        
        for (let i = 0; i < clickBuffer.length; i++) {
            const t = i / audioContext.sampleRate;
            const envelope = Math.exp(-t * 20);
            clickData[i] = Math.sin(2 * Math.PI * 1000 * t) * envelope * 0.2;
        }
        
        this.registerSound('click', clickBuffer);
    }

    createBackgroundMusic() {
        // Create ambient background music using oscillators
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        // We'll create this dynamically when needed to avoid memory issues
        this.musicConfig = {
            baseFrequency: 220,
            harmonics: [1, 1.5, 2, 2.5],
            tempo: 0.5
        };
    }

    registerSound(name, buffer) {
        this.sounds.set(name, buffer);
    }

    setupSoundPools() {
        // Pre-create sound sources for better performance
        const poolSize = 5;
        
        this.sounds.forEach((buffer, name) => {
            const pool = [];
            for (let i = 0; i < poolSize; i++) {
                pool.push(this.createSoundSource(buffer));
            }
            this.soundPools.set(name, pool);
        });
    }

    createSoundSource(buffer) {
        const audioContext = this.getAudioContext();
        if (!audioContext || !buffer) return null;
        
        return {
            buffer: buffer,
            isPlaying: false,
            source: null
        };
    }

    getAudioContext() {
        if (!this.audioContext) {
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } catch (e) {
                console.warn('SoundManager: Web Audio API not supported');
                return null;
            }
        }
        
        // Resume context if suspended (required by some browsers)
        if (this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
        
        return this.audioContext;
    }

    // Public sound playing methods
    playEat(pitch = 1.0) {
        if (!this.sfxEnabled) return;
        this.playSound('eat', this.sfxVolume * 0.8, pitch);
    }

    playBoost() {
        if (!this.sfxEnabled) return;
        this.playSound('boost', this.sfxVolume * 0.6);
    }

    playDeath() {
        if (!this.sfxEnabled) return;
        this.playSound('death', this.sfxVolume);
    }

    playClick() {
        if (!this.sfxEnabled) return;
        this.playSound('click', this.sfxVolume * 0.5);
    }

    playSound(name, volume = 1.0, pitch = 1.0) {
        const audioContext = this.getAudioContext();
        if (!audioContext || !this.enabled) return;
        
        const buffer = this.sounds.get(name);
        if (!buffer) return;
        
        try {
            const source = audioContext.createBufferSource();
            const gainNode = audioContext.createGain();
            
            source.buffer = buffer;
            source.playbackRate.value = pitch;
            
            gainNode.gain.value = volume * this.masterVolume;
            
            source.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            source.start();
            
            // Clean up after playing
            source.onended = () => {
                source.disconnect();
                gainNode.disconnect();
            };
            
        } catch (e) {
            console.warn('SoundManager: Error playing sound', name, e);
        }
    }

    startBackgroundMusic() {
        if (!this.musicEnabled || this.music) return;
        
        const audioContext = this.getAudioContext();
        if (!audioContext) return;
        
        try {
            // Create ambient music with multiple oscillators
            this.music = {
                oscillators: [],
                gainNodes: [],
                isPlaying: true
            };
            
            const config = this.musicConfig;
            
            config.harmonics.forEach((harmonic, index) => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.type = 'sine';
                oscillator.frequency.value = config.baseFrequency * harmonic;
                
                gainNode.gain.value = (this.musicVolume * this.masterVolume) / (index + 2);
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.start();
                
                this.music.oscillators.push(oscillator);
                this.music.gainNodes.push(gainNode);
            });
            
            console.log('SoundManager: Background music started');
            
        } catch (e) {
            console.warn('SoundManager: Error starting background music', e);
        }
    }

    stopBackgroundMusic() {
        if (!this.music) return;
        
        try {
            this.music.oscillators.forEach(osc => {
                osc.stop();
                osc.disconnect();
            });
            
            this.music.gainNodes.forEach(gain => {
                gain.disconnect();
            });
            
            this.music = null;
            
            console.log('SoundManager: Background music stopped');
            
        } catch (e) {
            console.warn('SoundManager: Error stopping background music', e);
        }
    }

    // Settings methods
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        this.saveUserPreferences();
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        if (this.music) {
            this.music.gainNodes.forEach((gain, index) => {
                gain.gain.value = (this.musicVolume * this.masterVolume) / (index + 2);
            });
        }
        
        this.saveUserPreferences();
    }

    setSfxVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        this.saveUserPreferences();
    }

    toggleMusic() {
        this.musicEnabled = !this.musicEnabled;
        
        if (this.musicEnabled) {
            this.startBackgroundMusic();
        } else {
            this.stopBackgroundMusic();
        }
        
        this.saveUserPreferences();
    }

    toggleSfx() {
        this.sfxEnabled = !this.sfxEnabled;
        this.saveUserPreferences();
    }

    // Persistence
    saveUserPreferences() {
        const prefs = {
            masterVolume: this.masterVolume,
            musicVolume: this.musicVolume,
            sfxVolume: this.sfxVolume,
            musicEnabled: this.musicEnabled,
            sfxEnabled: this.sfxEnabled
        };
        
        localStorage.setItem('slither_audio_prefs', JSON.stringify(prefs));
    }

    loadUserPreferences() {
        try {
            const prefs = JSON.parse(localStorage.getItem('slither_audio_prefs') || '{}');
            
            this.masterVolume = prefs.masterVolume ?? 0.7;
            this.musicVolume = prefs.musicVolume ?? 0.4;
            this.sfxVolume = prefs.sfxVolume ?? 0.6;
            this.musicEnabled = prefs.musicEnabled ?? true;
            this.sfxEnabled = prefs.sfxEnabled ?? true;
            
        } catch (e) {
            console.warn('SoundManager: Error loading preferences', e);
        }
    }

    // Cleanup
    destroy() {
        this.stopBackgroundMusic();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.sounds.clear();
        this.soundPools.clear();
        
        console.log('SoundManager: Destroyed');
    }
}
