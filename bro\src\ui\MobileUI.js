export default class MobileUI {
    constructor(scene) {
        this.scene = scene;
        this.isMobile = scene.game.isMobile;
        this.enabled = false;
        
        // UI elements
        this.joystick = null;
        this.boostButton = null;
        this.settingsButton = null;
        this.minimap = null;
        
        // Touch state
        this.touchState = {
            joystickActive: false,
            boostActive: false,
            joystickCenter: { x: 0, y: 0 },
            touchStart: { x: 0, y: 0 },
            currentTouch: { x: 0, y: 0 }
        };
        
        // UI configuration
        this.config = {
            joystickSize: 120,
            joystickDeadZone: 20,
            boostButtonSize: 80,
            minimapSize: 150,
            uiPadding: 20
        };
        
        console.log('MobileUI: Initialized for', this.isMobile ? 'mobile' : 'desktop');
    }

    enable() {
        if (this.enabled || !this.isMobile) return;
        
        this.enabled = true;
        this.createMobileControls();
        this.setupEventListeners();
        
        console.log('MobileUI: Enabled');
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.destroyMobileControls();
        this.removeEventListeners();
        
        console.log('MobileUI: Disabled');
    }

    createMobileControls() {
        const camera = this.scene.cameras.main;
        
        // Create virtual joystick
        this.createJoystick();
        
        // Create boost button
        this.createBoostButton();
        
        // Create settings button
        this.createSettingsButton();
        
        // Create minimap
        this.createMinimap();
        
        // Create performance indicator
        this.createPerformanceIndicator();
    }

    createJoystick() {
        const x = this.config.uiPadding + this.config.joystickSize / 2;
        const y = this.scene.cameras.main.height - this.config.uiPadding - this.config.joystickSize / 2;
        
        // Joystick base
        this.joystickBase = this.scene.add.graphics();
        this.joystickBase.fillStyle(0x000000, 0.3);
        this.joystickBase.fillCircle(0, 0, this.config.joystickSize / 2);
        this.joystickBase.lineStyle(3, 0x00ff88, 0.8);
        this.joystickBase.strokeCircle(0, 0, this.config.joystickSize / 2);
        this.joystickBase.setPosition(x, y);
        this.joystickBase.setScrollFactor(0);
        this.joystickBase.setDepth(1000);
        
        // Joystick knob
        this.joystickKnob = this.scene.add.graphics();
        this.joystickKnob.fillStyle(0x00ff88, 0.8);
        this.joystickKnob.fillCircle(0, 0, 25);
        this.joystickKnob.lineStyle(2, 0xffffff, 1);
        this.joystickKnob.strokeCircle(0, 0, 25);
        this.joystickKnob.setPosition(x, y);
        this.joystickKnob.setScrollFactor(0);
        this.joystickKnob.setDepth(1001);
        
        this.touchState.joystickCenter = { x, y };
    }

    createBoostButton() {
        const x = this.scene.cameras.main.width - this.config.uiPadding - this.config.boostButtonSize / 2;
        const y = this.scene.cameras.main.height - this.config.uiPadding - this.config.boostButtonSize / 2;
        
        this.boostButton = this.scene.add.graphics();
        this.boostButton.fillStyle(0xff6b6b, 0.7);
        this.boostButton.fillCircle(0, 0, this.config.boostButtonSize / 2);
        this.boostButton.lineStyle(3, 0xffffff, 1);
        this.boostButton.strokeCircle(0, 0, this.config.boostButtonSize / 2);
        this.boostButton.setPosition(x, y);
        this.boostButton.setScrollFactor(0);
        this.boostButton.setDepth(1000);
        this.boostButton.setInteractive(new Phaser.Geom.Circle(0, 0, this.config.boostButtonSize / 2), Phaser.Geom.Circle.Contains);
        
        // Add boost icon
        this.boostIcon = this.scene.add.text(x, y, '⚡', {
            fontSize: '32px',
            fill: '#ffffff'
        });
        this.boostIcon.setOrigin(0.5);
        this.boostIcon.setScrollFactor(0);
        this.boostIcon.setDepth(1001);
    }

    createSettingsButton() {
        const x = this.scene.cameras.main.width - this.config.uiPadding - 30;
        const y = this.config.uiPadding + 30;
        
        this.settingsButton = this.scene.add.graphics();
        this.settingsButton.fillStyle(0x000000, 0.5);
        this.settingsButton.fillCircle(0, 0, 25);
        this.settingsButton.lineStyle(2, 0x00ff88, 1);
        this.settingsButton.strokeCircle(0, 0, 25);
        this.settingsButton.setPosition(x, y);
        this.settingsButton.setScrollFactor(0);
        this.settingsButton.setDepth(1000);
        this.settingsButton.setInteractive(new Phaser.Geom.Circle(0, 0, 25), Phaser.Geom.Circle.Contains);
        
        // Add settings icon
        this.settingsIcon = this.scene.add.text(x, y, '⚙️', {
            fontSize: '20px'
        });
        this.settingsIcon.setOrigin(0.5);
        this.settingsIcon.setScrollFactor(0);
        this.settingsIcon.setDepth(1001);
    }

    createMinimap() {
        const x = this.config.uiPadding + this.config.minimapSize / 2;
        const y = this.config.uiPadding + this.config.minimapSize / 2;
        
        // Minimap background
        this.minimapBg = this.scene.add.graphics();
        this.minimapBg.fillStyle(0x000000, 0.6);
        this.minimapBg.fillCircle(0, 0, this.config.minimapSize / 2);
        this.minimapBg.lineStyle(3, 0x00ff88, 0.8);
        this.minimapBg.strokeCircle(0, 0, this.config.minimapSize / 2);
        this.minimapBg.setPosition(x, y);
        this.minimapBg.setScrollFactor(0);
        this.minimapBg.setDepth(1000);
        
        // Player dot on minimap
        this.minimapPlayer = this.scene.add.graphics();
        this.minimapPlayer.fillStyle(0x00ff88, 1);
        this.minimapPlayer.fillCircle(0, 0, 3);
        this.minimapPlayer.setPosition(x, y);
        this.minimapPlayer.setScrollFactor(0);
        this.minimapPlayer.setDepth(1001);
    }

    createPerformanceIndicator() {
        this.fpsText = this.scene.add.text(10, 10, 'FPS: 60', {
            fontSize: '14px',
            fill: '#00ff88',
            backgroundColor: 'rgba(0,0,0,0.5)',
            padding: { x: 5, y: 3 }
        });
        this.fpsText.setScrollFactor(0);
        this.fpsText.setDepth(1002);
        this.fpsText.setVisible(false); // Hidden by default
    }

    setupEventListeners() {
        // Joystick events
        this.scene.input.on('pointerdown', this.onPointerDown, this);
        this.scene.input.on('pointermove', this.onPointerMove, this);
        this.scene.input.on('pointerup', this.onPointerUp, this);
        
        // Boost button events
        this.boostButton.on('pointerdown', this.onBoostDown, this);
        this.boostButton.on('pointerup', this.onBoostUp, this);
        
        // Settings button events
        this.settingsButton.on('pointerdown', this.onSettingsClick, this);
        
        // Window resize
        this.scene.scale.on('resize', this.onResize, this);
    }

    removeEventListeners() {
        this.scene.input.off('pointerdown', this.onPointerDown, this);
        this.scene.input.off('pointermove', this.onPointerMove, this);
        this.scene.input.off('pointerup', this.onPointerUp, this);
        
        if (this.boostButton) {
            this.boostButton.off('pointerdown', this.onBoostDown, this);
            this.boostButton.off('pointerup', this.onBoostUp, this);
        }
        
        if (this.settingsButton) {
            this.settingsButton.off('pointerdown', this.onSettingsClick, this);
        }
        
        this.scene.scale.off('resize', this.onResize, this);
    }

    // Event handlers
    onPointerDown(pointer) {
        const distance = Phaser.Math.Distance.Between(
            pointer.x, pointer.y,
            this.touchState.joystickCenter.x, this.touchState.joystickCenter.y
        );
        
        if (distance <= this.config.joystickSize / 2) {
            this.touchState.joystickActive = true;
            this.touchState.touchStart = { x: pointer.x, y: pointer.y };
            this.updateJoystick(pointer);
        }
    }

    onPointerMove(pointer) {
        if (this.touchState.joystickActive) {
            this.updateJoystick(pointer);
        }
    }

    onPointerUp(pointer) {
        if (this.touchState.joystickActive) {
            this.touchState.joystickActive = false;
            this.resetJoystick();
        }
    }

    onBoostDown() {
        this.touchState.boostActive = true;
        this.boostButton.clear();
        this.boostButton.fillStyle(0xff6b6b, 1);
        this.boostButton.fillCircle(0, 0, this.config.boostButtonSize / 2);
        this.boostButton.lineStyle(3, 0xffffff, 1);
        this.boostButton.strokeCircle(0, 0, this.config.boostButtonSize / 2);
        
        // Play sound
        if (this.scene.soundManager) {
            this.scene.soundManager.playClick();
        }
    }

    onBoostUp() {
        this.touchState.boostActive = false;
        this.boostButton.clear();
        this.boostButton.fillStyle(0xff6b6b, 0.7);
        this.boostButton.fillCircle(0, 0, this.config.boostButtonSize / 2);
        this.boostButton.lineStyle(3, 0xffffff, 1);
        this.boostButton.strokeCircle(0, 0, this.config.boostButtonSize / 2);
    }

    onSettingsClick() {
        // Toggle settings panel
        this.scene.events.emit('toggleSettings');
        
        if (this.scene.soundManager) {
            this.scene.soundManager.playClick();
        }
    }

    onResize(gameSize) {
        // Reposition UI elements on resize
        this.repositionElements();
    }

    // Joystick methods
    updateJoystick(pointer) {
        const center = this.touchState.joystickCenter;
        const maxDistance = this.config.joystickSize / 2 - 25;
        
        let dx = pointer.x - center.x;
        let dy = pointer.y - center.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > maxDistance) {
            dx = (dx / distance) * maxDistance;
            dy = (dy / distance) * maxDistance;
        }
        
        this.joystickKnob.setPosition(center.x + dx, center.y + dy);
        
        // Calculate input values
        const inputX = dx / maxDistance;
        const inputY = dy / maxDistance;
        
        // Apply dead zone
        const inputMagnitude = Math.sqrt(inputX * inputX + inputY * inputY);
        if (inputMagnitude < this.config.joystickDeadZone / maxDistance) {
            this.touchState.currentTouch = { x: center.x, y: center.y };
        } else {
            // Convert to world coordinates
            const camera = this.scene.cameras.main;
            this.touchState.currentTouch = {
                x: camera.scrollX + camera.width / 2 + inputX * 200,
                y: camera.scrollY + camera.height / 2 + inputY * 200
            };
        }
    }

    resetJoystick() {
        const center = this.touchState.joystickCenter;
        this.joystickKnob.setPosition(center.x, center.y);
        this.touchState.currentTouch = { x: center.x, y: center.y };
    }

    repositionElements() {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        // Reposition joystick
        const joystickX = this.config.uiPadding + this.config.joystickSize / 2;
        const joystickY = height - this.config.uiPadding - this.config.joystickSize / 2;
        this.joystickBase.setPosition(joystickX, joystickY);
        this.joystickKnob.setPosition(joystickX, joystickY);
        this.touchState.joystickCenter = { x: joystickX, y: joystickY };
        
        // Reposition boost button
        const boostX = width - this.config.uiPadding - this.config.boostButtonSize / 2;
        const boostY = height - this.config.uiPadding - this.config.boostButtonSize / 2;
        this.boostButton.setPosition(boostX, boostY);
        this.boostIcon.setPosition(boostX, boostY);
        
        // Reposition settings button
        const settingsX = width - this.config.uiPadding - 30;
        const settingsY = this.config.uiPadding + 30;
        this.settingsButton.setPosition(settingsX, settingsY);
        this.settingsIcon.setPosition(settingsX, settingsY);
    }

    // Public methods
    getInput() {
        if (!this.enabled) return null;
        
        return {
            x: this.touchState.currentTouch.x,
            y: this.touchState.currentTouch.y,
            boost: this.touchState.boostActive,
            active: this.touchState.joystickActive
        };
    }

    updateMinimap(playerX, playerY, worldWidth, worldHeight) {
        if (!this.minimapPlayer) return;
        
        const center = {
            x: this.config.uiPadding + this.config.minimapSize / 2,
            y: this.config.uiPadding + this.config.minimapSize / 2
        };
        
        const mapRadius = this.config.minimapSize / 2 - 10;
        
        // Convert world position to minimap position
        const mapX = center.x + (playerX / worldWidth - 0.5) * mapRadius * 2;
        const mapY = center.y + (playerY / worldHeight - 0.5) * mapRadius * 2;
        
        this.minimapPlayer.setPosition(mapX, mapY);
    }

    updateFPS(fps) {
        if (this.fpsText) {
            this.fpsText.setText(`FPS: ${fps}`);
            
            // Color code based on performance
            if (fps >= 50) {
                this.fpsText.setFill('#00ff88');
            } else if (fps >= 30) {
                this.fpsText.setFill('#ffff00');
            } else {
                this.fpsText.setFill('#ff6b6b');
            }
        }
    }

    togglePerformanceIndicator() {
        if (this.fpsText) {
            this.fpsText.setVisible(!this.fpsText.visible);
        }
    }

    // Cleanup
    destroyMobileControls() {
        [this.joystickBase, this.joystickKnob, this.boostButton, this.boostIcon,
         this.settingsButton, this.settingsIcon, this.minimapBg, this.minimapPlayer,
         this.fpsText].forEach(element => {
            if (element && element.destroy) {
                element.destroy();
            }
        });
    }

    destroy() {
        this.disable();
        console.log('MobileUI: Destroyed');
    }
}
