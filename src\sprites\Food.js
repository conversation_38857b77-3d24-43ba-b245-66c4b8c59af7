import Phaser from 'phaser'

export default class extends Phaser.Sprite {

  constructor ({ game, x, y, color, size, id }) {
    super(game, x, y, color)

    this.game = game
    this.anchor.setTo(0.5)
    this.id = id
    this.size = size

    // Add glow effect: tween alpha between 0.5 and 1
    this.glowTween = this.game.add.tween(this)
      .to({ alpha: 0.5 }, 400, Phaser.Easing.Quadratic.InOut, true, 0, -1, true)
    this.alpha = 1
  }

  update () {
    
  }

}
