export default class InputManager {
    constructor(scene) {
        this.scene = scene;
        this.enabled = false;
        
        // Input state
        this.input = {
            x: 0,
            y: 0,
            boost: false,
            active: false
        };
        
        // Mobile detection
        this.isMobile = this.scene.game.isMobile;
        
        // Touch state
        this.touch = {
            active: false,
            startX: 0,
            startY: 0,
            currentX: 0,
            currentY: 0
        };
        
        // Keyboard state
        this.keys = {};
        
        console.log('InputManager: Initialized for', this.isMobile ? 'mobile' : 'desktop');
    }

    enable() {
        if (this.enabled) return;
        
        this.enabled = true;
        this.setupEventListeners();
        
        console.log('InputManager: Enabled');
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.removeEventListeners();
        
        console.log('InputManager: Disabled');
    }

    setupEventListeners() {
        // Mouse events (desktop)
        if (!this.isMobile) {
            this.scene.input.on('pointermove', this.onPointerMove, this);
            this.scene.input.on('pointerdown', this.onPointerDown, this);
            this.scene.input.on('pointerup', this.onPointerUp, this);
        }
        
        // Touch events (mobile)
        this.scene.input.on('pointerdown', this.onTouchStart, this);
        this.scene.input.on('pointermove', this.onTouchMove, this);
        this.scene.input.on('pointerup', this.onTouchEnd, this);
        
        // Keyboard events
        this.scene.input.keyboard.on('keydown', this.onKeyDown, this);
        this.scene.input.keyboard.on('keyup', this.onKeyUp, this);
        
        // Prevent context menu on mobile
        this.scene.game.canvas.addEventListener('contextmenu', this.preventDefault);
        
        // Prevent zoom on mobile
        this.scene.game.canvas.addEventListener('touchstart', this.preventZoom);
        this.scene.game.canvas.addEventListener('touchmove', this.preventZoom);
    }

    removeEventListeners() {
        // Remove all event listeners
        this.scene.input.off('pointermove', this.onPointerMove, this);
        this.scene.input.off('pointerdown', this.onPointerDown, this);
        this.scene.input.off('pointerup', this.onPointerUp, this);
        this.scene.input.off('pointerdown', this.onTouchStart, this);
        this.scene.input.off('pointermove', this.onTouchMove, this);
        this.scene.input.off('pointerup', this.onTouchEnd, this);
        
        if (this.scene.input.keyboard) {
            this.scene.input.keyboard.off('keydown', this.onKeyDown, this);
            this.scene.input.keyboard.off('keyup', this.onKeyUp, this);
        }
        
        this.scene.game.canvas.removeEventListener('contextmenu', this.preventDefault);
        this.scene.game.canvas.removeEventListener('touchstart', this.preventZoom);
        this.scene.game.canvas.removeEventListener('touchmove', this.preventZoom);
    }

    onPointerMove(pointer) {
        if (!this.enabled || this.isMobile) return;
        
        this.input.x = pointer.x;
        this.input.y = pointer.y;
        this.input.active = true;
    }

    onPointerDown(pointer) {
        if (!this.enabled || this.isMobile) return;
        
        this.input.boost = true;
        this.input.x = pointer.x;
        this.input.y = pointer.y;
        this.input.active = true;
    }

    onPointerUp(pointer) {
        if (!this.enabled || this.isMobile) return;
        
        this.input.boost = false;
    }

    onTouchStart(pointer) {
        if (!this.enabled) return;
        
        this.touch.active = true;
        this.touch.startX = pointer.x;
        this.touch.startY = pointer.y;
        this.touch.currentX = pointer.x;
        this.touch.currentY = pointer.y;
        
        this.input.x = pointer.x;
        this.input.y = pointer.y;
        this.input.boost = true; // Touch to boost
        this.input.active = true;
    }

    onTouchMove(pointer) {
        if (!this.enabled || !this.touch.active) return;
        
        this.touch.currentX = pointer.x;
        this.touch.currentY = pointer.y;
        
        this.input.x = pointer.x;
        this.input.y = pointer.y;
        this.input.active = true;
    }

    onTouchEnd(pointer) {
        if (!this.enabled) return;
        
        this.touch.active = false;
        this.input.boost = false;
    }

    onKeyDown(event) {
        if (!this.enabled) return;
        
        this.keys[event.code] = true;
        
        // Space bar for boost
        if (event.code === 'Space') {
            this.input.boost = true;
            event.preventDefault();
        }
        
        // Arrow keys for movement (fallback)
        this.updateKeyboardInput();
    }

    onKeyUp(event) {
        if (!this.enabled) return;
        
        this.keys[event.code] = false;
        
        if (event.code === 'Space') {
            this.input.boost = false;
        }
        
        this.updateKeyboardInput();
    }

    updateKeyboardInput() {
        // Fallback keyboard controls
        if (this.keys['ArrowLeft'] || this.keys['KeyA']) {
            this.input.x = this.scene.cameras.main.centerX - 100;
            this.input.y = this.scene.cameras.main.centerY;
            this.input.active = true;
        } else if (this.keys['ArrowRight'] || this.keys['KeyD']) {
            this.input.x = this.scene.cameras.main.centerX + 100;
            this.input.y = this.scene.cameras.main.centerY;
            this.input.active = true;
        }
        
        if (this.keys['ArrowUp'] || this.keys['KeyW']) {
            this.input.x = this.scene.cameras.main.centerX;
            this.input.y = this.scene.cameras.main.centerY - 100;
            this.input.active = true;
        } else if (this.keys['ArrowDown'] || this.keys['KeyS']) {
            this.input.x = this.scene.cameras.main.centerX;
            this.input.y = this.scene.cameras.main.centerY + 100;
            this.input.active = true;
        }
    }

    preventDefault(event) {
        event.preventDefault();
        return false;
    }

    preventZoom(event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    }

    update(time, delta) {
        if (!this.enabled) return;
        
        // Smooth input interpolation for better responsiveness
        if (this.input.active) {
            // Add slight smoothing to reduce jitter
            const smoothFactor = 0.8;
            this.input.x = this.input.x * smoothFactor + this.input.x * (1 - smoothFactor);
            this.input.y = this.input.y * smoothFactor + this.input.y * (1 - smoothFactor);
        }
    }

    getInput() {
        if (!this.enabled) return null;

        // Check if mobile UI is available and active
        if (this.scene.mobileUI && this.scene.mobileUI.enabled) {
            const mobileInput = this.scene.mobileUI.getInput();
            if (mobileInput && mobileInput.active) {
                return mobileInput;
            }
        }

        return { ...this.input };
    }

    // Utility methods
    isBoostActive() {
        return this.enabled && this.input.boost;
    }

    getInputPosition() {
        return this.enabled ? { x: this.input.x, y: this.input.y } : null;
    }

    isTouchActive() {
        return this.enabled && this.touch.active;
    }

    getTouchDistance() {
        if (!this.touch.active) return 0;
        
        const dx = this.touch.currentX - this.touch.startX;
        const dy = this.touch.currentY - this.touch.startY;
        return Math.sqrt(dx * dx + dy * dy);
    }
}
