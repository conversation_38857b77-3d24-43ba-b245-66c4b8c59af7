# Slither copy made with Phaser.io

While playing around with <PERSON>r, I tried to do a slither.io copy

## 1. Clone this repo:

Navigate into your workspace directory.

Run:

```git clone https://github.com/jernejc/slither-wannabe```

## 2. Install dependencies and run (make sure you have webpack installed):

Navigate to the cloned repo’s directory.

Run:

```npm install```

and

```npm run dev```

## Credits
Big thanks to this great repo:

https://github.com/lean/phaser-es6-webpack.git
