import Phaser from 'phaser';

export default class UIState extends Phaser.Scene {
    constructor() {
        super({ key: 'UIState' });
    }

    init() {
        // UI elements
        this.startScreen = null;
        this.hud = null;
        this.leaderboard = null;
        this.gameOverScreen = null;
        
        // Game state
        this.gameStarted = false;
        this.playerData = null;
    }

    create() {
        console.log('UIState: Creating UI...');
        
        // Get DOM elements
        this.setupDOMElements();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Show start screen
        this.showStartScreen();
        
        console.log('UIState: UI ready');
    }

    setupDOMElements() {
        // Get references to DOM elements
        this.startScreen = document.getElementById('startScreen');
        this.hud = document.getElementById('hud');
        this.leaderboard = document.getElementById('leaderboard');
        this.connectionStatus = document.getElementById('connectionStatus');
        
        // Input elements
        this.nicknameInput = document.getElementById('nicknameInput');
        this.playButton = document.getElementById('playButton');
        
        // HUD elements
        this.scoreElement = document.getElementById('score');
        this.lengthElement = document.getElementById('length');
        this.playerCountElement = document.getElementById('playerCount');
        this.leaderboardList = document.getElementById('leaderboardList');
    }

    setupEventListeners() {
        // Play button
        this.playButton.addEventListener('click', () => this.startGame());
        this.playButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startGame();
        });
        
        // Enter key in nickname input
        this.nicknameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.startGame();
            }
        });
        
        // Game events from GameState
        const gameScene = this.scene.get('GameState');
        
        gameScene.events.on('gameJoined', this.onGameJoined, this);
        gameScene.events.on('gameOver', this.onGameOver, this);
        gameScene.events.on('connectionStatusChanged', this.onConnectionStatusChanged, this);
        gameScene.events.on('leaderboardUpdate', this.onLeaderboardUpdate, this);
        gameScene.events.on('playerCountUpdate', this.onPlayerCountUpdate, this);
        gameScene.events.on('fpsUpdate', this.onFpsUpdate, this);
        
        // Focus nickname input on desktop
        if (!this.game.isMobile) {
            this.nicknameInput.focus();
        }
    }

    startGame() {
        const nickname = this.nicknameInput.value.trim() || 'Anonymous';
        
        if (nickname.length > 15) {
            this.showMessage('Nickname too long (max 15 characters)');
            return;
        }
        
        console.log('UIState: Starting game for:', nickname);
        
        this.playerData = {
            nickname: nickname,
            playerId: this.generatePlayerId()
        };
        
        // Hide start screen
        this.hideStartScreen();
        
        // Show game UI
        this.showGameUI();
        
        // Start the game
        const gameScene = this.scene.get('GameState');
        gameScene.events.emit('startGame', this.playerData);
        
        this.gameStarted = true;
    }

    generatePlayerId() {
        return 'player_' + Math.random().toString(36).substr(2, 9);
    }

    showStartScreen() {
        this.startScreen.style.display = 'block';
        this.hud.style.display = 'none';
        this.leaderboard.style.display = 'none';
    }

    hideStartScreen() {
        this.startScreen.style.display = 'none';
    }

    showGameUI() {
        this.hud.style.display = 'block';
        this.leaderboard.style.display = 'block';
        this.connectionStatus.style.display = 'block';
    }

    hideGameUI() {
        this.hud.style.display = 'none';
        this.leaderboard.style.display = 'none';
    }

    showGameOver(score) {
        // Create game over screen if it doesn't exist
        if (!this.gameOverScreen) {
            this.createGameOverScreen();
        }
        
        // Update score
        const finalScore = this.gameOverScreen.querySelector('#finalScore');
        if (finalScore) {
            finalScore.textContent = `Your Score: ${score}`;
        }
        
        this.gameOverScreen.style.display = 'block';
        this.hideGameUI();
    }

    createGameOverScreen() {
        this.gameOverScreen = document.createElement('div');
        this.gameOverScreen.id = 'gameOverScreen';
        this.gameOverScreen.innerHTML = `
            <h2>💀 GAME OVER 💀</h2>
            <div id="finalScore">Your Score: 0</div>
            <div style="margin-bottom: 20px; font-size: 16px; color: rgba(255,255,255,0.8);">
                You ran into another snake!
            </div>
            <button id="respawnButton">🔄 PLAY AGAIN 🔄</button>
        `;
        
        // Style the game over screen
        Object.assign(this.gameOverScreen.style, {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(40, 20, 20, 0.95))',
            padding: '50px',
            borderRadius: '30px',
            border: '4px solid #ff6b6b',
            boxShadow: '0 0 30px rgba(255, 107, 107, 0.3)',
            backdropFilter: 'blur(10px)',
            display: 'none',
            zIndex: '1001'
        });
        
        document.getElementById('ui-overlay').appendChild(this.gameOverScreen);
        
        // Add respawn button listener
        const respawnButton = this.gameOverScreen.querySelector('#respawnButton');
        respawnButton.addEventListener('click', () => this.respawn());
    }

    respawn() {
        if (this.gameOverScreen) {
            this.gameOverScreen.style.display = 'none';
        }
        
        // Reset game state
        this.gameStarted = false;
        
        // Show start screen again
        this.showStartScreen();
        
        // Reset nickname input
        this.nicknameInput.value = this.playerData?.nickname || '';
    }

    // Event handlers
    onGameJoined(data) {
        console.log('UIState: Game joined successfully');
        this.updateConnectionStatus(true);
    }

    onGameOver(data) {
        console.log('UIState: Game over with score:', data.score);
        this.showGameOver(data.score);
        this.gameStarted = false;
    }

    onConnectionStatusChanged(connected) {
        this.updateConnectionStatus(connected);
    }

    onLeaderboardUpdate(leaderboard) {
        this.updateLeaderboard(leaderboard);
    }

    onPlayerCountUpdate(count) {
        if (this.playerCountElement) {
            this.playerCountElement.textContent = count;
        }
    }

    onFpsUpdate(fps) {
        // Update FPS display if needed
        if (fps < 30) {
            console.warn('UIState: Low FPS detected:', fps);
        }
    }

    updateConnectionStatus(connected) {
        if (!this.connectionStatus) return;
        
        if (connected) {
            this.connectionStatus.textContent = 'Connected';
            this.connectionStatus.className = 'connected';
            this.connectionStatus.style.borderColor = '#00ff88';
            this.connectionStatus.style.color = '#00ff88';
        } else {
            this.connectionStatus.textContent = 'Disconnected - Reconnecting...';
            this.connectionStatus.className = 'disconnected';
            this.connectionStatus.style.borderColor = '#ff6b6b';
            this.connectionStatus.style.color = '#ff6b6b';
        }
    }

    updateLeaderboard(leaderboard) {
        if (!this.leaderboardList) return;
        
        this.leaderboardList.innerHTML = '';
        
        leaderboard.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item';
            
            const rank = index + 1;
            const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
            
            item.innerHTML = `
                <span class="player-name">${medal} ${player.nickname}</span>
                <span class="player-score">${player.score}</span>
            `;
            
            this.leaderboardList.appendChild(item);
        });
    }

    updateHUD(playerData) {
        if (!this.gameStarted) return;
        
        if (this.scoreElement && playerData.score !== undefined) {
            this.scoreElement.textContent = playerData.score;
        }
        
        if (this.lengthElement && playerData.length !== undefined) {
            this.lengthElement.textContent = Math.floor(playerData.length);
        }
    }

    showMessage(message, duration = 3000) {
        // Create temporary message element
        const messageElement = document.createElement('div');
        messageElement.textContent = message;
        
        Object.assign(messageElement.style, {
            position: 'absolute',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(255, 107, 107, 0.9)',
            color: 'white',
            padding: '10px 20px',
            borderRadius: '10px',
            fontSize: '16px',
            fontFamily: 'Orbitron',
            zIndex: '1002'
        });
        
        document.getElementById('ui-overlay').appendChild(messageElement);
        
        // Remove after duration
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, duration);
    }

    update() {
        if (!this.gameStarted) return;
        
        // Update HUD with current player data
        const gameScene = this.scene.get('GameState');
        if (gameScene.mySnake) {
            this.updateHUD({
                score: gameScene.mySnake.length * 10, // Simple score calculation
                length: gameScene.mySnake.length
            });
        }
    }
}
