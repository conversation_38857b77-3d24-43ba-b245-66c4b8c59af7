export default class CollisionManager {
    constructor(scene) {
        this.scene = scene;
        this.enabled = false;
        
        // Collision groups
        this.snakeHeadGroup = null;
        this.snakeBodyGroup = null;
        this.foodGroup = null;
        
        // Collision callbacks
        this.onFoodCollision = null;
        this.onSnakeCollision = null;
        this.onSelfCollision = null;
        
        console.log('CollisionManager: Initialized');
    }

    enable() {
        if (this.enabled) return;
        
        this.enabled = true;
        this.setupCollisionGroups();
        this.setupCollisionDetection();
        
        console.log('CollisionManager: Enabled');
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.removeCollisionDetection();
        
        console.log('CollisionManager: Disabled');
    }

    setupCollisionGroups() {
        // Create physics groups for different collision types
        this.snakeHeadGroup = this.scene.physics.add.group();
        this.snakeBodyGroup = this.scene.physics.add.group();
        this.foodGroup = this.scene.physics.add.group();
        
        console.log('CollisionManager: Groups created');
    }

    setupCollisionDetection() {
        // Snake head vs Food collision
        this.foodCollider = this.scene.physics.add.overlap(
            this.snakeHeadGroup,
            this.foodGroup,
            this.handleFoodCollision,
            this.processFoodCollision,
            this
        );

        // Snake head vs other snake bodies collision
        this.snakeCollider = this.scene.physics.add.overlap(
            this.snakeHeadGroup,
            this.snakeBodyGroup,
            this.handleSnakeCollision,
            this.processSnakeCollision,
            this
        );

        console.log('CollisionManager: Collision detection setup complete');
    }

    removeCollisionDetection() {
        if (this.foodCollider) {
            this.scene.physics.world.removeCollider(this.foodCollider);
            this.foodCollider = null;
        }

        if (this.snakeCollider) {
            this.scene.physics.world.removeCollider(this.snakeCollider);
            this.snakeCollider = null;
        }
    }

    // Add objects to collision groups
    addSnakeHead(snakeHead) {
        if (this.snakeHeadGroup) {
            this.snakeHeadGroup.add(snakeHead);
            console.log('CollisionManager: Snake head added to collision group');
        }
    }

    addSnakeBody(bodySegment) {
        if (this.snakeBodyGroup) {
            this.snakeBodyGroup.add(bodySegment);
        }
    }

    addFood(food) {
        if (this.foodGroup) {
            this.foodGroup.add(food);
        }
    }

    removeSnakeHead(snakeHead) {
        if (this.snakeHeadGroup) {
            this.snakeHeadGroup.remove(snakeHead);
        }
    }

    removeSnakeBody(bodySegment) {
        if (this.snakeBodyGroup) {
            this.snakeBodyGroup.remove(bodySegment);
        }
    }

    removeFood(food) {
        if (this.foodGroup) {
            this.foodGroup.remove(food);
        }
    }

    // Collision handlers
    handleFoodCollision(snakeHead, food) {
        if (!this.enabled) return;
        
        console.log('CollisionManager: Food collision detected');
        
        // Get the snake that owns this head
        const snake = snakeHead.snake;
        if (!snake || !snake.alive) return;
        
        // Only process collision for player snake
        if (!snake.config.isPlayer) return;
        
        // Consume the food
        const points = food.consume();
        
        // Grow the snake
        snake.grow(food.getSize());
        
        // Update score
        snake.score = (snake.score || 0) + points;
        
        // Emit event for UI update
        this.scene.events.emit('scoreUpdate', {
            playerId: snake.config.playerId,
            score: snake.score,
            length: snake.length
        });
        
        // Notify network manager
        if (this.scene.networkManager) {
            this.scene.networkManager.onFoodEaten(food.getId());
        }
        
        // Call custom callback if set
        if (this.onFoodCollision) {
            this.onFoodCollision(snake, food, points);
        }
    }

    processFoodCollision(snakeHead, food) {
        // Return true to allow collision processing
        return snakeHead.snake && snakeHead.snake.alive && snakeHead.snake.config.isPlayer;
    }

    handleSnakeCollision(snakeHead, bodySegment) {
        if (!this.enabled) return;
        
        console.log('CollisionManager: Snake collision detected');
        
        const attackingSnake = snakeHead.snake;
        const defendingSnake = bodySegment.snake;
        
        if (!attackingSnake || !defendingSnake) return;
        if (!attackingSnake.alive || !defendingSnake.alive) return;
        
        // Check if it's self-collision
        if (attackingSnake === defendingSnake) {
            this.handleSelfCollision(attackingSnake);
            return;
        }
        
        // Only kill player snake
        if (attackingSnake.config.isPlayer) {
            this.killSnake(attackingSnake, defendingSnake);
        }
    }

    processSnakeCollision(snakeHead, bodySegment) {
        const attackingSnake = snakeHead.snake;
        const defendingSnake = bodySegment.snake;
        
        // Don't process collision if either snake is dead
        if (!attackingSnake?.alive || !defendingSnake?.alive) return false;
        
        // For self-collision, only check if snake is long enough and segment is far enough
        if (attackingSnake === defendingSnake) {
            const segmentIndex = defendingSnake.bodySegments.indexOf(bodySegment);
            return defendingSnake.length > 8 && segmentIndex > 5;
        }
        
        return true;
    }

    handleSelfCollision(snake) {
        console.log('CollisionManager: Self-collision detected');
        
        if (this.onSelfCollision) {
            this.onSelfCollision(snake);
        } else {
            this.killSnake(snake, null);
        }
    }

    killSnake(snake, killedBy = null) {
        console.log(`CollisionManager: Killing snake ${snake.config.nickname}`);
        
        // Mark snake as dead
        snake.kill();
        
        // Create death effect
        this.createDeathEffect(snake);
        
        // Emit game over event for player
        if (snake.config.isPlayer) {
            this.scene.events.emit('gameOver', {
                score: snake.score || 0,
                length: snake.length,
                killedBy: killedBy?.config.nickname || 'yourself'
            });
            
            // Notify network manager
            if (this.scene.networkManager) {
                this.scene.networkManager.onPlayerKilled(killedBy?.config.playerId);
            }
        }
        
        // Call custom callback if set
        if (this.onSnakeCollision) {
            this.onSnakeCollision(snake, killedBy);
        }
        
        // Convert snake body to food (like Slither.io)
        this.convertSnakeToFood(snake);
    }

    createDeathEffect(snake) {
        // Large explosion effect
        const particles = this.scene.add.particles(snake.head.x, snake.head.y, 'particle', {
            speed: { min: 100, max: 200 },
            scale: { start: 0.8, end: 0 },
            tint: [0xff6b6b, 0xffffff, 0xffaa00],
            lifespan: 1500,
            quantity: 30
        });
        
        // Screen shake for player death
        if (snake.config.isPlayer) {
            this.scene.cameras.main.shake(500, 0.02);
        }
        
        // Remove particles after animation
        this.scene.time.delayedCall(1500, () => {
            particles.destroy();
        });
    }

    convertSnakeToFood(snake) {
        // Convert each body segment to food
        snake.bodySegments.forEach((segment, index) => {
            if (!segment.visible) return;
            
            // Create food at segment position with slight randomization
            const offsetX = Phaser.Math.Between(-10, 10);
            const offsetY = Phaser.Math.Between(-10, 10);
            
            const food = this.scene.add.existing(
                new (require('../objects/Food').default)(
                    this.scene,
                    segment.x + offsetX,
                    segment.y + offsetY,
                    {
                        color: 'pink', // Dead snake becomes pink food
                        size: 2,
                        value: 15
                    }
                )
            );
            
            this.addFood(food);
            
            // Delay the food creation for visual effect
            food.setAlpha(0);
            this.scene.tweens.add({
                targets: food,
                alpha: 1,
                duration: 200,
                delay: index * 50,
                ease: 'Power2.easeOut'
            });
        });
    }

    // Manual collision checking for precise control
    checkCollisions() {
        if (!this.enabled) return;
        
        // This can be used for additional collision checks
        // that need more precise control than Phaser's built-in system
    }

    // Utility methods
    setFoodCollisionCallback(callback) {
        this.onFoodCollision = callback;
    }

    setSnakeCollisionCallback(callback) {
        this.onSnakeCollision = callback;
    }

    setSelfCollisionCallback(callback) {
        this.onSelfCollision = callback;
    }

    // Cleanup
    destroy() {
        this.disable();
        
        this.snakeHeadGroup = null;
        this.snakeBodyGroup = null;
        this.foodGroup = null;
        
        this.onFoodCollision = null;
        this.onSnakeCollision = null;
        this.onSelfCollision = null;
        
        console.log('CollisionManager: Destroyed');
    }
}
