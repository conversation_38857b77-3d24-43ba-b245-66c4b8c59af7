<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <title>Slither.io Clone - Phaser Edition</title>
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: radial-gradient(circle at center, #0a0a0a 0%, #1a1a1a 50%, #000000 100%);
            overflow: hidden;
            color: white;
            touch-action: none;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        /* UI Overlay */
        #ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        /* Start Screen */
        #startScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 40, 0.95));
            padding: 50px;
            border-radius: 30px;
            border: 4px solid #00ff88;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
            pointer-events: all;
            backdrop-filter: blur(10px);
        }

        #startScreen h1 {
            font-size: 64px;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #00ff88, #00ccff, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
            to { text-shadow: 0 0 30px rgba(0, 255, 136, 0.8); }
        }

        #nicknameInput {
            padding: 20px 25px;
            font-size: 20px;
            border: 3px solid #00ff88;
            border-radius: 15px;
            margin: 30px 0;
            width: 350px;
            background: rgba(0, 0, 0, 0.7);
            color: #00ff88;
            text-align: center;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
        }

        #playButton {
            padding: 20px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            color: #000;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
        }

        /* HUD */
        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.5);
            padding: 15px 20px;
            border-radius: 15px;
            border: 2px solid #00ff88;
            pointer-events: all;
        }

        /* Leaderboard */
        #leaderboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            min-width: 280px;
            border: 2px solid #00ccff;
            pointer-events: all;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            #startScreen {
                width: 90%;
                max-width: 400px;
                padding: 30px 20px;
            }
            
            #startScreen h1 {
                font-size: 48px;
            }
            
            #nicknameInput {
                width: 100%;
                font-size: 18px;
            }
            
            #hud, #leaderboard {
                font-size: 14px;
                padding: 10px 15px;
            }
        }

        /* Loading indicator */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 136, 0.3);
            border-radius: 50%;
            border-top-color: #00ff88;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="gameContainer"></div>
    
    <div id="ui-overlay">
        <!-- Start Screen -->
        <div id="startScreen">
            <h1>🐍 SLITHER.IO</h1>
            <div style="font-size: 18px; margin-bottom: 20px; color: #00ccff;">
                Powered by Phaser.js - Smooth & Fast!
            </div>
            <input type="text" id="nicknameInput" placeholder="Enter your nickname" maxlength="15">
            <br>
            <button id="playButton">⚡ PLAY ⚡</button>
        </div>

        <!-- Game HUD -->
        <div id="hud" style="display: none;">
            <div>🏆 Score: <span id="score">0</span></div>
            <div>📏 Length: <span id="length">1</span></div>
            <div>👥 Players: <span id="playerCount">1</span></div>
        </div>

        <!-- Leaderboard -->
        <div id="leaderboard" style="display: none;">
            <h3>🏆 Leaderboard</h3>
            <div id="leaderboardList"></div>
        </div>

        <!-- Connection Status -->
        <div id="connectionStatus" style="position: absolute; bottom: 20px; left: 20px; padding: 10px; background: rgba(0,0,0,0.7); border-radius: 10px; display: none;">
            <span class="loading"></span> Connecting...
        </div>
    </div>
</body>
</html>
