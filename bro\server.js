const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const compression = require('compression');
const helmet = require('helmet');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// Production middleware
app.use(compression()); // Gzip compression
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "blob:"],
            connectSrc: ["'self'", "ws:", "wss:"],
            mediaSrc: ["'self'"],
            objectSrc: ["'none'"],
            frameSrc: ["'none'"]
        }
    }
})); // Security headers

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production'
        ? ['https://yourdomain.com', 'https://www.yourdomain.com']
        : true,
    credentials: true
}));

// Socket.io with CORS
const io = socketIo(server, {
    cors: {
        origin: process.env.NODE_ENV === 'production'
            ? ['https://yourdomain.com', 'https://www.yourdomain.com']
            : true,
        methods: ["GET", "POST"]
    }
});

// Game configuration
const CONFIG = {
    WORLD_WIDTH: 4000,
    WORLD_HEIGHT: 4000,
    FOOD_COUNT: 1000,
    SNAKE_SPEED: 2.0, // Reduced for better multiplayer gameplay
    FOOD_SIZE: 8,
    SNAKE_SEGMENT_SIZE: 12,
    GROWTH_RATE: 3,
    MAX_PLAYERS: 100,
    TURN_SPEED: 0.12, // Slightly reduced for more controlled movement
    BOOST_SPEED: 3.2, // Speed when boosting
    BOOST_DRAIN: 0.8 // How much boost drains per second
};

// Game state
let players = new Map();
let food = new Map(); // Changed to Map for better performance
let leaderboard = [];

// Color palettes
const foodColors = [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7',
    '#dda0dd', '#98d8c8', '#f7dc6f', '#bb8fce', '#85c1e9',
    '#f8c471', '#82e0aa', '#f1948a', '#85929e', '#d5a6bd',
    '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
    '#ee5a24', '#009432', '#0984e3', '#6c5ce7', '#fd79a8'
];

const snakeColors = [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7',
    '#dda0dd', '#98d8c8', '#f7dc6f', '#bb8fce', '#85c1e9',
    '#f8c471', '#82e0aa', '#f1948a', '#85929e', '#d5a6bd'
];

// Serve static files with caching
app.use(express.static(path.join(__dirname, 'public'), {
    maxAge: process.env.NODE_ENV === 'production' ? '1d' : 0,
    etag: true,
    lastModified: true
}));

// Serve the main HTML file
app.get('/', (req, res) => {
    res.setHeader('Cache-Control', 'no-cache');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        players: players.size,
        food: food.size,
        uptime: process.uptime()
    });
});

// API endpoint for game stats
app.get('/api/stats', (req, res) => {
    res.json({
        players: players.size,
        food: food.size,
        leaderboard: leaderboard.slice(0, 10)
    });
});

// Generate random food
function generateFood() {
    food.clear(); // Clear the Map

    for (let i = 0; i < CONFIG.FOOD_COUNT; i++) {
        const foodItem = {
            id: uuidv4(),
            x: Math.random() * CONFIG.WORLD_WIDTH,
            y: Math.random() * CONFIG.WORLD_HEIGHT,
            color: getRandomFoodColor(),
            size: getRandomFoodSize()
        };
        food.set(foodItem.id, foodItem);
    }
}

// Get random food color with weighted distribution
function getRandomFoodColor() {
    const colors = ['pink', 'blue', 'green', 'lime', 'red'];
    const weights = [60, 20, 15, 4, 1]; // Pink most common, red rarest

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.floor(Math.random() * totalWeight) + 1;

    for (let i = 0; i < colors.length; i++) {
        random -= weights[i];
        if (random <= 0) {
            return colors[i];
        }
    }

    return 'pink';
}

// Get random food size based on color
function getRandomFoodSize() {
    return Math.random() < 0.8 ? 1 : Math.random() < 0.9 ? 2 : 3;
}

// Create a new snake
function createSnake(nickname, socketId) {
    const startX = Math.random() * (CONFIG.WORLD_WIDTH - 200) + 100;
    const startY = Math.random() * (CONFIG.WORLD_HEIGHT - 200) + 100;
    
    return {
        id: socketId,
        nickname: nickname || 'Anonymous',
        x: startX,
        y: startY,
        segments: [{
            x: startX,
            y: startY
        }],
        angle: Math.random() * Math.PI * 2,
        speed: CONFIG.SNAKE_SPEED,
        score: 0,
        length: 1,
        color: snakeColors[Math.floor(Math.random() * snakeColors.length)],
        alive: true,
        lastUpdate: Date.now(),
        birthTime: Date.now()
    };
}

// Update snake position
function updateSnake(snake, targetX, targetY) {
    if (!snake.alive) return;

    const dx = targetX - snake.x;
    const dy = targetY - snake.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance > 3) { // Reduced from 5 for more sensitivity
        const targetAngle = Math.atan2(dy, dx);
        
        // Smooth turning instead of instant direction change
        let angleDiff = targetAngle - snake.angle;
        
        // Normalize angle difference to [-π, π]
        while (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
        while (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
        
        // Apply turn speed limit for smoother movement
        if (Math.abs(angleDiff) > CONFIG.TURN_SPEED) {
            snake.angle += Math.sign(angleDiff) * CONFIG.TURN_SPEED;
        } else {
            snake.angle = targetAngle;
        }
        
        // Move snake in current direction
        snake.x += Math.cos(snake.angle) * snake.speed;
        snake.y += Math.sin(snake.angle) * snake.speed;

        // Keep snake within bounds with some margin
        snake.x = Math.max(20, Math.min(CONFIG.WORLD_WIDTH - 20, snake.x));
        snake.y = Math.max(20, Math.min(CONFIG.WORLD_HEIGHT - 20, snake.y));

        // Update segments
        snake.segments.unshift({ x: snake.x, y: snake.y });
        while (snake.segments.length > snake.length) {
            snake.segments.pop();
        }
    }
}

// Check collision with food
function checkFoodCollision(snake) {
    if (!snake.alive) return;

    for (let i = food.length - 1; i >= 0; i--) {
        const foodItem = food[i];
        const dx = snake.x - foodItem.x;
        const dy = snake.y - foodItem.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < CONFIG.SNAKE_SEGMENT_SIZE) {
            // Snake ate food
            snake.score += 10;
            snake.length += CONFIG.GROWTH_RATE;
            
            // Remove eaten food and generate new one
            food.splice(i, 1);
            food.push({
                id: uuidv4(),
                x: Math.random() * CONFIG.WORLD_WIDTH,
                y: Math.random() * CONFIG.WORLD_HEIGHT,
                color: foodColors[Math.floor(Math.random() * foodColors.length)],
                size: CONFIG.FOOD_SIZE
            });
        }
    }
}

// Check collision with other snakes
function checkSnakeCollision(snake) {
    if (!snake.alive) return;

    // Skip collision check for very new snakes (first 2 seconds)
    if (Date.now() - snake.birthTime < 2000) {
        return;
    }

    for (let [playerId, otherSnake] of players) {
        if (playerId === snake.id || !otherSnake.alive) continue;

        // Check collision with other snake's segments
        for (let segment of otherSnake.segments) {
            const dx = snake.x - segment.x;
            const dy = snake.y - segment.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < CONFIG.SNAKE_SEGMENT_SIZE - 2) { // Make collision slightly more forgiving
                snake.alive = false;
                return;
            }
        }
    }

    // Check self-collision (only if snake is long enough and has been alive for a while)
    if (snake.segments.length > 8) { // Increased from 5 to 8 segments
        for (let i = 8; i < snake.segments.length; i++) {
            const segment = snake.segments[i];
            const dx = snake.x - segment.x;
            const dy = snake.y - segment.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < CONFIG.SNAKE_SEGMENT_SIZE - 2) { // Make collision slightly more forgiving
                snake.alive = false;
                return;
            }
        }
    }
}

// Update leaderboard
function updateLeaderboard() {
    leaderboard = Array.from(players.values())
        .filter(player => player.alive)
        .sort((a, b) => b.score - a.score)
        .slice(0, 10)
        .map(player => ({
            nickname: player.nickname,
            score: player.score
        }));
}

// Initialize game
generateFood();

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log(`Player connected: ${socket.id}`);

    socket.on('join-game', (data) => {
        const nickname = data.nickname || 'Anonymous';
        const snake = createSnake(nickname, socket.id);
        players.set(socket.id, snake);

        socket.emit('game-joined', {
            playerId: socket.id,
            snake: snake,
            worldSize: {
                width: CONFIG.WORLD_WIDTH,
                height: CONFIG.WORLD_HEIGHT
            }
        });

        updateLeaderboard();
        io.emit('leaderboard-update', leaderboard);
        io.emit('player-count', players.size);

        console.log(`Player ${nickname} joined the game`);
    });

    socket.on('move', (data) => {
        const snake = players.get(socket.id);
        if (snake && snake.alive) {
            // Handle boost
            if (data.boost && snake.length > 5) {
                snake.speed = CONFIG.BOOST_SPEED;
                snake.length = Math.max(5, snake.length - CONFIG.BOOST_DRAIN);
            } else {
                snake.speed = CONFIG.SNAKE_SPEED;
            }
            
            updateSnake(snake, data.x, data.y);
            checkFoodCollision(snake);
            checkSnakeCollision(snake);

            if (!snake.alive) {
                console.log(`Player ${snake.nickname} died with score ${snake.score}`);
                socket.emit('game-over', { score: snake.score });
            }
        }
    });

    socket.on('food-eaten', (data) => {
        const snake = players.get(socket.id);
        if (snake && snake.alive && food.has(data.foodId)) {
            const foodItem = food.get(data.foodId);

            // Remove the eaten food
            food.delete(data.foodId);

            // Grow the snake
            snake.length += foodItem.size || 1;
            snake.score += (foodItem.size || 1) * 10;

            // Spawn new food to replace eaten food
            const newFood = {
                id: uuidv4(),
                x: Math.random() * CONFIG.WORLD_WIDTH,
                y: Math.random() * CONFIG.WORLD_HEIGHT,
                color: getRandomFoodColor(),
                size: getRandomFoodSize()
            };
            food.set(newFood.id, newFood);

            console.log(`Food eaten by ${snake.nickname}: +${(foodItem.size || 1) * 10} points`);
        }
    });

    socket.on('respawn', (data) => {
        const nickname = data.nickname || 'Anonymous';
        const snake = createSnake(nickname, socket.id);
        players.set(socket.id, snake);

        socket.emit('respawned', {
            playerId: socket.id,
            snake: snake
        });

        updateLeaderboard();
        io.emit('leaderboard-update', leaderboard);
    });

    socket.on('disconnect', () => {
        players.delete(socket.id);
        updateLeaderboard();
        io.emit('leaderboard-update', leaderboard);
        io.emit('player-count', players.size);
        console.log(`Player disconnected: ${socket.id}`);
    });
});

// Game loop
setInterval(() => {
    // Send game state to all players
    const alivePlayers = Array.from(players.values()).filter(p => p.alive);
    const gameState = {
        players: alivePlayers,
        food: Array.from(food.values()), // Convert Map to Array for client
        timestamp: Date.now()
    };

    // Debug logging every 3 seconds
    if (Date.now() % 3000 < 100) {
        console.log(`Game state: ${alivePlayers.length} alive players, ${food.length} food items`);
        alivePlayers.forEach(p => {
            console.log(`Player ${p.nickname}: x=${p.x.toFixed(1)}, y=${p.y.toFixed(1)}, segments=${p.segments.length}`);
        });
    }

    io.emit('game-state', gameState);
    
    // Update leaderboard every few seconds
    if (Date.now() % 5000 < 100) {
        updateLeaderboard();
        io.emit('leaderboard-update', leaderboard);
    }
}, 1000 / 60); // 60 FPS

const PORT = process.env.PORT || 4000;
const HOST = process.env.HOST || '0.0.0.0';

// Error handling
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
        process.exit(0);
    });
});

server.listen(PORT, HOST, () => {
    console.log(`🐍 Slither.io Production Server`);
    console.log(`🚀 Running on http://${HOST}:${PORT}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`👥 Max players: ${CONFIG.MAX_PLAYERS}`);
    console.log(`🍎 Food items: ${CONFIG.FOOD_COUNT}`);
    console.log(`⚡ Game loop: 60 FPS`);
    console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
});
