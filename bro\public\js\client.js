// Client-side socket handling and game initialization
class GameClient {
    constructor() {
        this.socket = null;
        this.game = null;
        this.connected = false;
        this.nickname = '';
        this.playerId = null;
        
        this.initializeUI();
        this.connectToServer();
    }

    initializeUI() {
        const playButton = document.getElementById('playButton');
        const respawnButton = document.getElementById('respawnButton');
        const nicknameInput = document.getElementById('nicknameInput');
        
        // Add both click and touch events for better mobile support
        playButton.addEventListener('click', () => this.joinGame());
        playButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.joinGame();
        });
        
        respawnButton.addEventListener('click', () => this.respawn());
        respawnButton.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.respawn();
        });
        
        nicknameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.joinGame();
            }
        });
        
        // Focus on nickname input (but not on mobile to avoid keyboard issues)
        if (!this.isMobile()) {
            nicknameInput.focus();
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
               window.innerWidth <= 768;
    }

    connectToServer() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.connected = true;
            this.updateConnectionStatus();
            console.log('Connected to server');
        });
        
        this.socket.on('disconnect', () => {
            this.connected = false;
            this.updateConnectionStatus();
            console.log('Disconnected from server');
        });
        
        this.socket.on('game-joined', (data) => {
            this.playerId = data.playerId;
            window.myPlayerId = data.playerId;
            
            // Initialize game
            this.game = new Game();
            this.game.worldSize = data.worldSize;
            this.game.myPlayer = data.snake;
            
            // Hide start screen
            document.getElementById('startScreen').style.display = 'none';
            
            // Start sending movement updates
            this.startMovementUpdates();
            
            console.log('Joined game as:', data.snake.nickname);
        });
        
        this.socket.on('game-state', (gameState) => {
            if (this.game) {
                this.game.updateGameState(gameState);
                this.updateUI();
            }
        });
        
        this.socket.on('game-over', (data) => {
            this.showGameOver(data.score);
        });
        
        this.socket.on('respawned', (data) => {
            this.playerId = data.playerId;
            window.myPlayerId = data.playerId;
            
            if (this.game) {
                this.game.myPlayer = data.snake;
            }
            
            document.getElementById('gameOverScreen').style.display = 'none';
            this.startMovementUpdates();
        });
        
        this.socket.on('leaderboard-update', (leaderboard) => {
            this.updateLeaderboard(leaderboard);
        });
        
        this.socket.on('player-count', (count) => {
            document.getElementById('playerCount').textContent = count;
        });
        
        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            this.connected = false;
            this.updateConnectionStatus();
        });
    }

    joinGame() {
        const nicknameInput = document.getElementById('nicknameInput');
        this.nickname = nicknameInput.value.trim() || 'Anonymous';
        
        if (this.socket && this.connected) {
            this.socket.emit('join-game', { nickname: this.nickname });
        }
    }

    respawn() {
        if (this.socket && this.connected) {
            this.socket.emit('respawn', { nickname: this.nickname });
        }
    }

    startMovementUpdates() {
        if (this.movementInterval) {
            clearInterval(this.movementInterval);
        }
        
        // Use different update rates for mobile vs desktop
        const updateRate = this.game?.isMobile ? 60 : 120; // Lower rate for mobile to save battery
        
        this.movementInterval = setInterval(() => {
            if (this.game && this.game.myPlayer && this.game.myPlayer.alive) {
                const worldMouse = this.game.getWorldMousePosition();
                const isBoosting = this.game.keys[' '] || this.game.keys['shift'] || this.game.mouse.down;
                
                this.socket.emit('move', {
                    x: worldMouse.x,
                    y: worldMouse.y,
                    boost: isBoosting
                });
            }
        }, 1000 / updateRate);
    }

    showGameOver(score) {
        document.getElementById('finalScore').textContent = `Your Score: ${score}`;
        document.getElementById('gameOverScreen').style.display = 'block';
        
        if (this.movementInterval) {
            clearInterval(this.movementInterval);
        }
    }

    updateLeaderboard(leaderboard) {
        const leaderboardList = document.getElementById('leaderboardList');
        leaderboardList.innerHTML = '';
        
        leaderboard.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item';
            
            const rank = index + 1;
            const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
            
            item.innerHTML = `
                <span class="player-name">${medal} ${player.nickname}</span>
                <span class="player-score">${player.score}</span>
            `;
            
            leaderboardList.appendChild(item);
        });
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        if (this.connected) {
            statusElement.textContent = 'Connected';
            statusElement.className = 'connected';
        } else {
            statusElement.textContent = 'Disconnected - Reconnecting...';
            statusElement.className = 'disconnected';
        }
    }

    updateUI() {
        if (this.game && this.game.myPlayer) {
            const player = this.game.myPlayer;
            
            // Update score and length
            document.getElementById('score').textContent = player.score || 0;
            document.getElementById('length').textContent = player.length || 1;
            
            // Update boost meter (boost available if length > 5)
            const boostFill = document.getElementById('boostFill');
            if (boostFill) {
                const boostAvailable = Math.max(0, (player.length || 1) - 5);
                const maxBoost = 50; // Maximum boost value for UI
                const boostPercentage = Math.min(100, (boostAvailable / maxBoost) * 100);
                boostFill.style.width = boostPercentage + '%';
                
                // Change color based on boost level
                if (boostPercentage > 60) {
                    boostFill.style.background = 'linear-gradient(90deg, #00ff88, #00ccff)';
                } else if (boostPercentage > 30) {
                    boostFill.style.background = 'linear-gradient(90deg, #ffcc00, #ff6b6b)';
                } else {
                    boostFill.style.background = 'linear-gradient(90deg, #ff6b6b, #cc0000)';
                }
            }
        }
    }
}

// Initialize game client when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.gameClient = new GameClient();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, reduce update frequency
        console.log('Page hidden, reducing updates');
    } else {
        // Page is visible, resume normal updates
        console.log('Page visible, resuming updates');
    }
});

// Handle beforeunload to clean up
window.addEventListener('beforeunload', () => {
    if (window.gameClient && window.gameClient.socket) {
        window.gameClient.socket.disconnect();
    }
});
