module.exports = {
  apps: [{
    name: 'slither-game',
    script: 'server.js',
    instances: 1, // Single instance for socket.io
    exec_mode: 'fork', // Fork mode for socket.io compatibility
    
    // Environment variables
    env: {
      NODE_ENV: 'development',
      PORT: 4000,
      HOST: '0.0.0.0'
    },
    
    env_production: {
      NODE_ENV: 'production',
      PORT: 4000,
      HOST: '0.0.0.0'
    },
    
    // Logging
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Auto restart configuration
    watch: false, // Disable in production
    ignore_watch: ['node_modules', 'logs', 'public'],
    
    // Memory and CPU limits
    max_memory_restart: '500M',
    
    // Restart configuration
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Advanced PM2 features
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // Monitoring
    monitoring: false, // Set to true if using PM2 Plus
    
    // Auto restart on file changes (development only)
    watch_options: {
      followSymlinks: false,
      usePolling: false
    }
  }],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server-ip',
      ref: 'origin/main',
      repo: '**************:yourusername/slither-game.git',
      path: '/var/www/slither-game',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
