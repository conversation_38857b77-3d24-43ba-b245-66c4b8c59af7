# 🐍 Slither.io Clone

A real-time multiplayer snake game inspired by Slither.io, built with Node.js, Express, and Socket.io.

## Features

✅ **Real-time multiplayer gameplay** - Play with up to 100 players simultaneously
✅ **Smooth snake movement** - Fluid controls with mouse/touch support
✅ **Food spawning and collection** - Eat colorful food to grow your snake
✅ **Collision detection** - Snake vs snake and snake vs self collision
✅ **Leaderboard** - Real-time top 10 player rankings
✅ **Respawn/restart logic** - Quick respawn after game over
✅ **Basic UI** - Score display, nickname input, and game over screen
✅ **Minimap** - Overview of the game world and other players
✅ **Mobile support** - Touch controls for mobile devices

## Screenshots

The game features:
- Beautiful gradient background
- Smooth snake animations with eyes
- Glowing food particles
- Real-time leaderboard
- Minimap showing player positions
- Responsive design for all devices

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd snither
```

2. Install dependencies:
```bash
npm install
```

3. Start the server:
```bash
npm start
```

4. Open your browser and navigate to:
```
http://localhost:3000
```

## Development

For development with auto-restart:
```bash
npm run dev
```

## Game Controls

- **Mouse**: Move your snake by pointing where you want to go
- **Touch**: On mobile devices, touch and drag to control your snake
- **Enter**: Start the game or respawn after game over

## Game Mechanics

- **Growing**: Eat food to increase your score and length
- **Collision**: Avoid hitting other snakes or yourself
- **Scoring**: Each food item gives you 10 points
- **Length**: Each food item increases your length by 3 segments
- **World Size**: 4000x4000 pixel game world
- **Food Count**: 1000 food items spawned at random locations

## Technical Details

### Server (Node.js)
- Express.js for web server
- Socket.io for real-time communication
- Game loop running at 60 FPS
- Collision detection and game state management

### Client (Vanilla JavaScript)
- HTML5 Canvas for game rendering
- Real-time socket communication
- Smooth camera following
- Minimap implementation
- Mobile-responsive design

### Game Architecture
- **Server**: Authoritative game logic, collision detection, food spawning
- **Client**: Rendering, input handling, UI management
- **Communication**: Real-time updates via WebSockets

## Configuration

You can modify game settings in `server.js`:

```javascript
const CONFIG = {
    WORLD_WIDTH: 4000,      // Game world width
    WORLD_HEIGHT: 4000,     // Game world height
    FOOD_COUNT: 1000,       // Number of food items
    SNAKE_SPEED: 2,         // Snake movement speed
    FOOD_SIZE: 8,           // Food particle size
    SNAKE_SEGMENT_SIZE: 12, // Snake segment size
    GROWTH_RATE: 3,         // Segments added per food
    MAX_PLAYERS: 100        // Maximum players
};
```

## Deployment

### Local Development
```bash
npm start
```

### Production Deployment
1. Set the PORT environment variable
2. Use a process manager like PM2
3. Configure reverse proxy (nginx) if needed

Example with PM2:
```bash
npm install -g pm2
pm2 start server.js --name "slither-game"
```

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Mobile browsers with Canvas support

## Performance Tips

- Game runs at 60 FPS on both server and client
- Only visible objects are rendered
- Efficient collision detection algorithms
- Optimized network communication

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this project for learning or commercial purposes.

## Future Enhancements

Possible future features:
- [ ] Boost/speed mechanic
- [ ] Different game modes
- [ ] Skins and customization
- [ ] Power-ups
- [ ] Sound effects
- [ ] Particle effects
- [ ] Spectator mode
- [ ] Replay system

## Credits

Created as a learning project to demonstrate real-time multiplayer game development with modern web technologies.
