<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <title>Slither.io Clone</title>
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: radial-gradient(circle at center, #0a0a0a 0%, #1a1a1a 50%, #000000 100%);
            background-size: 100% 100%;
            overflow: hidden;
            color: white;
            touch-action: none;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #gameCanvas {
            background: radial-gradient(circle at center, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            display: block;
            cursor: none;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        #startScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 40, 0.95));
            padding: 50px;
            border-radius: 30px;
            border: 4px solid #00ff88;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.3), inset 0 0 30px rgba(0, 255, 136, 0.1);
            pointer-events: all;
            backdrop-filter: blur(10px);
        }

        #startScreen h1 {
            font-size: 64px;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #00ff88, #00ccff, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
            font-weight: 900;
            animation: glow 2s ease-in-out infinite alternate;
        }

        .game-instructions {
            margin: 20px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            border: 2px solid rgba(0, 255, 136, 0.2);
            font-size: 16px;
            text-align: left;
        }

        .game-instructions h3 {
            color: #00ff88;
            margin-bottom: 10px;
            text-align: center;
        }

        .game-instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .game-instructions li {
            margin: 5px 0;
            color: rgba(255, 255, 255, 0.8);
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
            to { text-shadow: 0 0 30px rgba(0, 255, 136, 0.8), 0 0 40px rgba(0, 255, 136, 0.6); }
        }

        #nicknameInput {
            padding: 20px 25px;
            font-size: 20px;
            border: 3px solid #00ff88;
            border-radius: 15px;
            margin: 30px 0;
            width: 350px;
            background: rgba(0, 0, 0, 0.7);
            color: #00ff88;
            text-align: center;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
            transition: all 0.3s ease;
        }

        #nicknameInput:focus {
            outline: none;
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.6);
            border-color: #00ccff;
        }

        #nicknameInput::placeholder {
            color: rgba(0, 255, 136, 0.6);
        }

        #playButton {
            padding: 20px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            color: #000;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.4);
            position: relative;
            overflow: hidden;
        }

        #playButton:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 30px rgba(0, 255, 136, 0.6);
        }

        #playButton:active {
            transform: translateY(0);
        }

        #gameOverScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(40, 20, 20, 0.95));
            padding: 50px;
            border-radius: 30px;
            border: 4px solid #ff6b6b;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
            pointer-events: all;
            display: none;
            backdrop-filter: blur(10px);
        }

        #gameOverScreen h2 {
            font-size: 48px;
            margin-bottom: 30px;
            color: #ff6b6b;
            font-weight: 900;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        #finalScore {
            font-size: 32px;
            margin-bottom: 30px;
            color: #fff;
            font-weight: 700;
        }

        #respawnButton {
            padding: 20px 40px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
        }

        #respawnButton:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 30px rgba(255, 107, 107, 0.6);
        }

        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 20px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            font-weight: 700;
            background: rgba(0, 0, 0, 0.5);
            padding: 15px 20px;
            border-radius: 15px;
            border: 2px solid #00ff88;
            backdrop-filter: blur(5px);
        }

        #hud div {
            margin-bottom: 8px;
        }

        #hud div:last-child {
            margin-bottom: 0;
        }

        #boostMeter {
            margin-top: 15px;
        }

        .boost-label {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .boost-bar {
            width: 120px;
            height: 8px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .boost-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffcc00);
            width: 100%;
            transition: width 0.2s ease;
            box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
        }

        #leaderboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            min-width: 280px;
            border: 2px solid #00ccff;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 20px rgba(0, 204, 255, 0.3);
        }

        #leaderboard h3 {
            color: #00ccff;
            margin-bottom: 15px;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(0, 204, 255, 0.5);
        }

        .leaderboard-item {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 700;
        }

        .leaderboard-item:last-child {
            border-bottom: none;
        }

        .player-name {
            color: #fff;
            font-size: 16px;
        }

        .player-score {
            color: #00ff88;
            font-weight: bold;
            font-size: 16px;
        }

        #connectionStatus {
            position: absolute;
            bottom: 20px;
            left: 20px;
            padding: 12px 20px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            font-size: 14px;
            border: 2px solid;
            backdrop-filter: blur(5px);
            font-weight: 700;
        }

        .connected {
            color: #00ff88;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .disconnected {
            color: #ff6b6b;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }

        #minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 70%, rgba(0, 0, 0, 0.95) 100%);
            border: 3px solid #00ff88;
            border-radius: 50%;
            backdrop-filter: blur(5px);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.4), inset 0 0 20px rgba(0, 255, 136, 0.1);
            overflow: hidden;
        }

        #minimap::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 50%;
            pointer-events: none;
        }

        /* Pulsing effect for score */
        .score-pulse {
            animation: scorePulse 0.3s ease-out;
        }

        @keyframes scorePulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); color: #00ff88; }
            100% { transform: scale(1); }
        }

        /* Loading animation */
        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 255, 136, 0.3);
            border-top: 2px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            #startScreen {
                width: 90%;
                max-width: 400px;
                padding: 30px 20px;
                margin: 0 auto;
            }

            #startScreen h1 {
                font-size: 48px;
                margin-bottom: 20px;
            }

            #nicknameInput {
                width: 100%;
                padding: 15px 20px;
                font-size: 18px;
                margin: 20px 0;
            }

            #playButton, #respawnButton {
                width: 100%;
                padding: 15px 25px;
                font-size: 18px;
                margin: 15px 0;
            }

            .game-instructions {
                font-size: 14px;
                padding: 15px;
            }

            .game-instructions h3 {
                font-size: 16px;
            }

            #hud {
                top: 10px;
                left: 10px;
                padding: 12px 15px;
                font-size: 14px;
                border-radius: 10px;
            }

            #hud div {
                margin-bottom: 6px;
            }

            #boostMeter {
                margin-top: 10px;
            }

            .boost-bar {
                width: 100px;
                height: 6px;
            }

            #leaderboard {
                top: 10px;
                right: 10px;
                padding: 12px 15px;
                font-size: 12px;
                border-radius: 10px;
                max-width: 150px;
            }

            #leaderboard h3 {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .leaderboard-item {
                padding: 4px 0;
                font-size: 11px;
            }

            #minimap {
                width: 100px;
                height: 100px;
                bottom: 10px;
                right: 10px;
            }

            #connectionStatus {
                bottom: 10px;
                left: 10px;
                font-size: 12px;
                padding: 6px 10px;
            }

            #gameOverScreen {
                width: 90%;
                max-width: 350px;
                padding: 30px 20px;
            }

            #gameOverScreen h2 {
                font-size: 28px;
                margin-bottom: 15px;
            }

            #finalScore {
                font-size: 18px;
                margin-bottom: 20px;
            }
        }

        /* Touch Controls Indicator */
        #touchControls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: #00ff88;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            display: none;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            #touchControls {
                display: block;
            }
        }

        /* Landscape orientation adjustments */
        @media (max-width: 768px) and (orientation: landscape) {
            #startScreen {
                width: 60%;
                max-width: 500px;
                padding: 20px;
            }

            #startScreen h1 {
                font-size: 36px;
                margin-bottom: 15px;
            }

            .game-instructions {
                font-size: 12px;
                padding: 10px;
            }

            #nicknameInput {
                padding: 12px 18px;
                font-size: 16px;
            }

            #playButton, #respawnButton {
                padding: 12px 20px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div id="startScreen">
                <h1>🐍 SLITHER.IO</h1>
                <div style="font-size: 18px; margin-bottom: 20px; color: #00ccff;">
                    Eat to grow longer! Don't run into other snakes!
                </div>
                <div class="game-instructions">
                    <h3>🎮 How to Play</h3>
                    <ul>
                        <li>Move your mouse to control your snake</li>
                        <li>Hold <strong>SPACE</strong> or <strong>CLICK</strong> to boost (costs length)</li>
                        <li>Eat glowing food to grow longer</li>
                        <li>Avoid hitting other snakes or yourself</li>
                        <li>Cut off other snakes to make them crash!</li>
                    </ul>
                </div>
                <input type="text" id="nicknameInput" placeholder="Enter your nickname" maxlength="15">
                <br>
                <button id="playButton">⚡ PLAY ⚡</button>
                <div style="margin-top: 20px; font-size: 14px; color: rgba(255,255,255,0.7);">
                    Use your mouse to move
                </div>
            </div>

            <div id="gameOverScreen">
                <h2>💀 GAME OVER 💀</h2>
                <div id="finalScore">Your Score: 0</div>
                <div style="margin-bottom: 20px; font-size: 16px; color: rgba(255,255,255,0.8);">
                    You ran into another snake!
                </div>
                <button id="respawnButton">🔄 PLAY AGAIN 🔄</button>
            </div>

            <div id="hud">
                <div>🏆 Score: <span id="score">0</span></div>
                <div>📏 Length: <span id="length">1</span></div>
                <div>👥 Players: <span id="playerCount">1</span></div>
                <div id="boostMeter">
                    <div class="boost-label">⚡ Boost</div>
                    <div class="boost-bar">
                        <div class="boost-fill" id="boostFill"></div>
                    </div>
                </div>
            </div>

            <div id="leaderboard">
                <h3>🏆 Leaderboard</h3>
                <div id="leaderboardList"></div>
            </div>

            <div id="connectionStatus" class="connected">
                Connected
            </div>

            <div id="touchControls">
                👆 Touch to move • Hold to boost
            </div>

            <canvas id="minimap"></canvas>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="js/game.js"></script>
    <script src="js/client.js"></script>
</body>
</html>
