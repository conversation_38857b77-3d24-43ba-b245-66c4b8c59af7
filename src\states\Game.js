
import Phaser from 'phaser'
import Food from '../sprites/Food'
import Player from '../objects/Player'
import {setResponsiveWidth} from '../utils'
import io from 'socket.io-client'

export default class extends Phaser.State {
  init () {
    this.foodGroup;
    this.players;
  }

  preload () {
    console.log('preload game')
  }

  create () {
    this.game.physics.startSystem(Phaser.Physics.ARCADE);
    this.game.world.setBounds(0, 0, 4600, 4600);
    this.players = [];

    // Connect to socket
    this.socket = io(window.location.origin);
    
    // Init me and make the camera follow me
    this.player = new Player({
      index: ["me"],
      game: this.game,
      x: this.game.world.centerX,
      y: this.game.world.centerY,
      numSnakeSections: 30,
      socket: this.socket,
      assets: {
        head: 'head',
        body: 'body'
      }
    })
    this.game.camera.follow(this.player.snakeHead);

    // Add foodGroup and generate a few randoms ones
    this.foodGroup = this.game.add.physicsGroup();

    // set the sprite width to 30% of the game width
    //setResponsiveWidth(this.snakeHead, 30, this.game.world)
    //this.game.add.existing(this.player.snakeHead)

    // Socket connection successful
    this.socket.on('connect', this._onSocketConnected.bind(this))

    // Socket disconnection
    this.socket.on('disconnect', this._onSocketDisconnect)

    // New player message received
    this.socket.on('food group', this._onFoodGroup.bind(this))

    // New player message received
    this.socket.on('new player', this._onNewPlayer.bind(this))

    // Move player
    this.socket.on('move player', this._onMovePlayer.bind(this))

    // Grow player
    this.socket.on('grow player', this._onGrowPlayer.bind(this))

    // Rotate player
    this.socket.on('rotate player', this._onRotatePlayer.bind(this))

    // Player removed message received
    this.socket.on('remove player', this._onRemovePlayer.bind(this))
  }

  update () {
    if (this.game.physics.arcade.collide(this.player.snakeHead, this.foodGroup, this._handleFoodColision, this._processHandler, this)) {
      console.log('nom nom nom');
    }

    // Update player
    this.player.update(true);

    // Update other players
    for (var i = 0; i < this.players.length; i++) {
      if (this.players[i].alive) {
        this.players[i].update();
        // Check collision between your snake head and each segment of other players
        for (let j = 1; j < this.players[i].snakeSection.length; j++) {
          let section = this.players[i].snakeSection[j];
          if (section) {
            this.game.physics.arcade.collide(this.player.snakeHead, section, this._handlePlayerColision, null, this);
          }
        }
      }
    }
  }

  render () {
    // Collision detection between player's snake head and other players' bodies
    if (this.player && this.player.snakeHead) {
      for (let i = 0; i < this.players.length; i++) {
        const p = this.players[i];
        if (p && p.snakeSection && p.alive) {
          for (let j = 1; j < p.snakeSection.length; j++) {
            const section = p.snakeSection[j];
            if (section) {
              const dx = this.player.snakeHead.x - section.x;
              const dy = this.player.snakeHead.y - section.y;
              const dist = Math.sqrt(dx * dx + dy * dy);
              if (dist < 16) { // collision radius
                // Handle collision: e.g., kill player, show effect, etc.
                console.log('Collision detected with another snake!');
                // Example: kill player
                this.player.snakeHead.kill();
                // You can add more logic here (emit event, show message, etc.)
              }
            }
          }
        }
      }
    }
    // Debug: print number of players and their positions
    let debugSnakes = [];
    if (this.player && this.player.snakeHead) {
      debugSnakes.push({id: 'me', x: this.player.snakeHead.x, y: this.player.snakeHead.y, section: this.player.snakeSection});
    }
    for (let i = 0; i < this.players.length; i++) {
      const p = this.players[i];
      if (p && p.snakeHead && p.alive) {
        debugSnakes.push({id: p.id, x: p.snakeHead.x, y: p.snakeHead.y, section: p.snakeSection});
      }
    }
    console.log('Minimap snakes:', debugSnakes);
    console.log('All players:', this.players);
    // Minimap settings
    const minimapWidth = 180;
    const minimapHeight = 180;
    const minimapX = this.game.width - minimapWidth - 20;
    const minimapY = 20;
    const worldWidth = this.game.world.width;
    const worldHeight = this.game.world.height;

    // Use Phaser.Graphics for minimap
    if (!this.minimapGraphics) {
      this.minimapGraphics = this.game.add.graphics(0, 0);
      this.minimapGraphics.fixedToCamera = true;
    }
    const g = this.minimapGraphics;
    g.clear();
    g.alpha = 0.7;
    g.beginFill(0x222222);
    g.drawRect(minimapX, minimapY, minimapWidth, minimapHeight);
    g.endFill();
    g.alpha = 1.0;

    // Draw border
    g.lineStyle(2, 0xffffff, 1);
    g.drawRect(minimapX, minimapY, minimapWidth, minimapHeight);

    // Helper to draw snake body
    function drawSnakeBodyPhaser(snakeSections, color) {
      if (!snakeSections || snakeSections.length === 0) return;
      g.lineStyle(2, color, 1);
      let started = false;
      for (let i = 1; i < snakeSections.length; i++) {
        const section = snakeSections[i];
        if (!section) continue;
        const x = minimapX + (section.x / worldWidth) * minimapWidth;
        const y = minimapY + (section.y / worldHeight) * minimapHeight;
        if (!started) {
          g.moveTo(x, y);
          started = true;
        } else {
          g.lineTo(x, y);
        }
      }
    }

    // Helper to draw snake head and direction
    function drawSnakeHeadWithDirectionPhaser(snakeHead, color) {
      if (!snakeHead) return;
      const x = minimapX + (snakeHead.x / worldWidth) * minimapWidth;
      const y = minimapY + (snakeHead.y / worldHeight) * minimapHeight;
      g.beginFill(color);
      g.drawCircle(x, y, 10);
      g.endFill();
      // Draw direction arrow
      if (snakeHead.rotation !== undefined) {
        const arrowLength = 15;
        const dx = Math.cos(snakeHead.rotation) * arrowLength;
        const dy = Math.sin(snakeHead.rotation) * arrowLength;
        g.lineStyle(2, color, 1);
        g.moveTo(x, y);
        g.lineTo(x + dx, y + dy);
      }
    }

    // Draw moving dot for your snake head on minimap
    if (this.player && this.player.snakeHead) {
      const x = minimapX + (this.player.snakeHead.x / worldWidth) * minimapWidth;
      const y = minimapY + (this.player.snakeHead.y / worldHeight) * minimapHeight;
      g.beginFill(0x00ff00, 1);
      g.drawCircle(x, y, 8);
      g.endFill();
    }

    // Draw moving dot for other players' snake heads on minimap
    for (let i = 0; i < this.players.length; i++) {
      const p = this.players[i];
      if (p && p.snakeHead && p.alive) {
        const x = minimapX + (p.snakeHead.x / worldWidth) * minimapWidth;
        const y = minimapY + (p.snakeHead.y / worldHeight) * minimapHeight;
        g.beginFill(0xff0000, 1);
        g.drawCircle(x, y, 8);
        g.endFill();
      }
    }
  }

  /******************** 
  ** Private helpers **
  ********************/

  _handleFoodColision (head, food) {
    console.log('_handleColision food', food);

    this.player.grow(food)
    food.kill()
  }

  _handlePlayerColision (me, them) {
    console.log('_handlePlayerColision', me, them)
    // Game over logic: kill the snake and show game over message
    if (me && me.kill) {
      me.kill();
      alert('Game Over! You collided with another snake.');
      // Optionally, you can restart the game or show a restart button here
    }
  }

  _processHandler (head, food) { 
    console.log('_processHandler', arguments);

    return true;
  }

  /*******************
  ** Event handlers **
  *******************/

  _onSocketConnected () {
    console.log('_onSocketConnected Connected to socket server')

    // Reset enemies on reconnect
    this.players.forEach(function(player) {
      player.kill()
    })
    this.players = [];

    // Send local player data to the game server
    this.socket.emit('new player', { x: this.player.snakeHead.x, y: this.player.snakeHead.y, numSnakeSections: this.numSnakeSections, angle: this.player.snakeHead.body.angularVelocity })
  }

  _onFoodGroup (data) {
    var foodGroup = this.foodGroup;
    var game = this.game;

    data.forEach(function(foodItem) {
      foodGroup.add(new Food({
        game: game,
        x: foodItem.id.x, 
        y: foodItem.id.y, 
        color: foodItem.id.color,
        size: foodItem.id.size,
        id: foodItem.id.id
      }))
    })

    console.log('foodGroup', foodGroup);
  }

  _onNewPlayer (data) {
    console.log('New player connected:', data)
    console.log('Current players before add:', this.players);

    // Avoid possible duplicate players
    var duplicate = this.players.find(function(player){
      return player.id == data.id;
    })
    if (duplicate) {
      console.log('Duplicate player!', duplicate);
      return
    }

    // Add new player to the remote players array
    const newPlayer = new Player({
      index: data.id,
      game: this.game,
      x: this.game.world.centerX,
      y: this.game.world.centerY,
      numSnakeSections: data.numSnakeSections,
      socket: this.socket,
      assets: {
        head: 'headE',
        body: 'bodyE'
      }
    });
    console.log('Adding new player:', newPlayer);
    this.players.push(newPlayer);
    console.log('Current players after add:', this.players);
  }

  _onMovePlayer (data) {
    console.log('_onMovePlayer', data, this.players);
    var player = this.players.find(function(player){
      return player.id == data.id;
    })

    // Player not found
    if (!player) {
      console.log('Player not found in move:', data.id, 'Current players:', this.players);
      return
    }

    // Update player position
    player.movePlayer(data.x, data.y)
  }

  _onRotatePlayer (data) {
    //console.log('_onRotatePlayer', data);
    var player = this.players.find(function(player){
      return player.id == data.id;
    })

    // Player not found
    if (!player) {
      console.log('Player not found: ', data.id)
      return
    }

    // Update player position
    player.rotateHead(data.angle)
  }

  _onGrowPlayer (data) {
    console.log('_onGrowPlayer', data);
    var player = this.players.find(function(player){
      return player.id == data.id;
    })

    // Player not found
    if (!player) {
      console.log('Player not found: ', data.id)
      return
    }

    // Update player position
    player.grow(data.size, true)

    // Remove food item
    var food = this.foodGroup.children.find(function(foodItem) {
      return foodItem.id == data.foodId;
    })

    if(food)
      this.foodGroup.remove(food);
  }

  _onRemovePlayer (data) {
    console.log('_onRemovePlayer', data);
    console.log('Current players before remove:', this.players);
    var player = this.players.find(function(player){
      return player.id == data.id;
    })

    // Player not found
    if (!player) {
      console.log('Player not found in remove:', data.id, 'Current players:', this.players);
      return
    }

    //player.player.kill()

    // Remove player from array
    this.players.splice(this.players.indexOf(player), 1)
    console.log('Current players after remove:', this.players);
  }
}
