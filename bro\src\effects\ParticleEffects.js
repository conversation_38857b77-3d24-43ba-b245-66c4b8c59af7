export default class ParticleEffects {
    constructor(scene) {
        this.scene = scene;
        this.effects = new Map();
        this.pools = new Map();
        
        // Effect configurations
        this.configs = {
            foodEat: {
                speed: { min: 30, max: 80 },
                scale: { start: 0.4, end: 0 },
                lifespan: 600,
                quantity: 8,
                blendMode: 'ADD'
            },
            snakeDeath: {
                speed: { min: 100, max: 200 },
                scale: { start: 0.8, end: 0 },
                lifespan: 1500,
                quantity: 30,
                blendMode: 'ADD'
            },
            snakeTrail: {
                speed: { min: 10, max: 30 },
                scale: { start: 0.2, end: 0 },
                lifespan: 800,
                quantity: 2,
                blendMode: 'ADD',
                frequency: 100
            },
            boost: {
                speed: { min: 50, max: 100 },
                scale: { start: 0.3, end: 0 },
                lifespan: 400,
                quantity: 5,
                blendMode: 'ADD',
                frequency: 50
            },
            foodSpawn: {
                speed: { min: 20, max: 50 },
                scale: { start: 0, end: 0.5 },
                lifespan: 1000,
                quantity: 12,
                blendMode: 'NORMAL'
            }
        };
        
        console.log('ParticleEffects: Initialized');
    }

    init() {
        this.createParticleTextures();
        this.setupPools();
        
        console.log('ParticleEffects: Ready');
    }

    createParticleTextures() {
        // Create various particle textures
        this.createCircleParticle('particle_white', 0xffffff, 4);
        this.createCircleParticle('particle_glow', 0x00ff88, 6);
        this.createStarParticle('particle_star', 0xffff00, 8);
        this.createSparkParticle('particle_spark', 0xff6b6b, 3);
        
        // Create trail particle
        this.createTrailParticle('particle_trail', 0x00ccff, 2);
    }

    createCircleParticle(key, color, size) {
        const graphics = this.scene.add.graphics();
        graphics.fillStyle(color, 1);
        graphics.fillCircle(size/2, size/2, size/2);
        
        // Add glow effect
        graphics.fillStyle(color, 0.3);
        graphics.fillCircle(size/2, size/2, size);
        
        graphics.generateTexture(key, size * 2, size * 2);
        graphics.destroy();
    }

    createStarParticle(key, color, size) {
        const graphics = this.scene.add.graphics();
        graphics.fillStyle(color, 1);
        
        // Draw star shape
        const points = [];
        for (let i = 0; i < 10; i++) {
            const angle = (i * Math.PI) / 5;
            const radius = i % 2 === 0 ? size : size / 2;
            points.push(size + Math.cos(angle) * radius);
            points.push(size + Math.sin(angle) * radius);
        }
        
        graphics.fillPoints(points, true);
        graphics.generateTexture(key, size * 2, size * 2);
        graphics.destroy();
    }

    createSparkParticle(key, color, size) {
        const graphics = this.scene.add.graphics();
        graphics.lineStyle(2, color, 1);
        
        // Draw spark lines
        graphics.moveTo(0, size);
        graphics.lineTo(size * 2, size);
        graphics.moveTo(size, 0);
        graphics.lineTo(size, size * 2);
        
        graphics.generateTexture(key, size * 2, size * 2);
        graphics.destroy();
    }

    createTrailParticle(key, color, size) {
        const graphics = this.scene.add.graphics();
        graphics.fillStyle(color, 0.6);
        graphics.fillCircle(size, size, size);
        
        graphics.generateTexture(key, size * 2, size * 2);
        graphics.destroy();
    }

    setupPools() {
        // Pre-create particle emitters for better performance
        Object.keys(this.configs).forEach(effectType => {
            this.pools.set(effectType, []);
        });
    }

    // Main effect methods
    createFoodEatEffect(x, y, color = 0x00ff88) {
        const config = {
            ...this.configs.foodEat,
            tint: [color, 0xffffff, color],
            x: x,
            y: y
        };
        
        return this.createEffect('foodEat', x, y, 'particle_glow', config);
    }

    createSnakeDeathEffect(x, y, snakeColor = 0xff6b6b) {
        // Main explosion
        const mainConfig = {
            ...this.configs.snakeDeath,
            tint: [snakeColor, 0xffffff, 0xff0000],
            x: x,
            y: y
        };
        
        const mainEffect = this.createEffect('snakeDeath', x, y, 'particle_white', mainConfig);
        
        // Secondary star burst
        const starConfig = {
            speed: { min: 80, max: 150 },
            scale: { start: 0.6, end: 0 },
            lifespan: 1200,
            quantity: 15,
            tint: [0xffff00, 0xff6b6b],
            blendMode: 'ADD'
        };
        
        const starEffect = this.createEffect('snakeDeathStar', x, y, 'particle_star', starConfig);
        
        // Screen shake effect
        this.scene.cameras.main.shake(500, 0.02);
        
        return [mainEffect, starEffect];
    }

    createSnakeTrailEffect(snake) {
        if (!snake.alive || !snake.config.isPlayer) return;
        
        const head = snake.head;
        const config = {
            ...this.configs.snakeTrail,
            tint: snake.config.color || 0x00ff88,
            follow: head,
            followOffset: { x: 0, y: 0 }
        };
        
        const effectId = `trail_${snake.config.playerId}`;
        
        if (!this.effects.has(effectId)) {
            const effect = this.createContinuousEffect(effectId, head.x, head.y, 'particle_trail', config);
            this.effects.set(effectId, effect);
        }
        
        return this.effects.get(effectId);
    }

    createBoostEffect(snake) {
        if (!snake.alive || !snake.isBoosting) return;
        
        const head = snake.head;
        const config = {
            ...this.configs.boost,
            tint: [0xffff00, 0xff6b6b, 0x00ff88],
            follow: head,
            followOffset: { x: -20, y: 0 }
        };
        
        const effectId = `boost_${snake.config.playerId}`;
        
        if (!this.effects.has(effectId)) {
            const effect = this.createContinuousEffect(effectId, head.x, head.y, 'particle_spark', config);
            this.effects.set(effectId, effect);
        }
        
        return this.effects.get(effectId);
    }

    createFoodSpawnEffect(x, y, foodColor = 0x00ff88) {
        const config = {
            ...this.configs.foodSpawn,
            tint: [foodColor, 0xffffff],
            x: x,
            y: y
        };
        
        return this.createEffect('foodSpawn', x, y, 'particle_white', config);
    }

    // Core effect creation methods
    createEffect(type, x, y, texture, config) {
        const particles = this.scene.add.particles(x, y, texture, {
            speed: config.speed,
            scale: config.scale,
            lifespan: config.lifespan,
            quantity: config.quantity,
            tint: config.tint,
            blendMode: config.blendMode || 'NORMAL',
            emitZone: config.emitZone
        });
        
        // Auto-destroy after lifespan
        this.scene.time.delayedCall(config.lifespan + 100, () => {
            if (particles && particles.destroy) {
                particles.destroy();
            }
        });
        
        return particles;
    }

    createContinuousEffect(id, x, y, texture, config) {
        const particles = this.scene.add.particles(x, y, texture, {
            speed: config.speed,
            scale: config.scale,
            lifespan: config.lifespan,
            frequency: config.frequency || 100,
            tint: config.tint,
            blendMode: config.blendMode || 'NORMAL'
        });
        
        // Follow target if specified
        if (config.follow) {
            particles.startFollow(config.follow, config.followOffset?.x || 0, config.followOffset?.y || 0);
        }
        
        return particles;
    }

    // Effect management
    stopEffect(effectId) {
        const effect = this.effects.get(effectId);
        if (effect) {
            effect.stop();
            this.scene.time.delayedCall(1000, () => {
                if (effect.destroy) {
                    effect.destroy();
                }
                this.effects.delete(effectId);
            });
        }
    }

    stopSnakeEffects(playerId) {
        this.stopEffect(`trail_${playerId}`);
        this.stopEffect(`boost_${playerId}`);
    }

    // Update method for continuous effects
    update(time, delta) {
        // Update any time-based effects here
        this.effects.forEach((effect, id) => {
            if (!effect.active) {
                this.effects.delete(id);
            }
        });
    }

    // Cleanup
    destroy() {
        this.effects.forEach(effect => {
            if (effect.destroy) {
                effect.destroy();
            }
        });
        
        this.effects.clear();
        this.pools.clear();
        
        console.log('ParticleEffects: Destroyed');
    }
}
