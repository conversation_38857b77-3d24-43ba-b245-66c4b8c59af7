export default class PerformanceMonitor {
    constructor(scene) {
        this.scene = scene;
        this.enabled = false;
        
        // Performance metrics
        this.metrics = {
            fps: 60,
            frameTime: 16.67,
            memoryUsage: 0,
            networkLatency: 0,
            renderCalls: 0,
            activeObjects: 0
        };
        
        // Tracking arrays
        this.fpsHistory = [];
        this.frameTimeHistory = [];
        this.maxHistoryLength = 60; // 1 second at 60fps
        
        // Timing
        this.lastFrameTime = performance.now();
        this.frameCount = 0;
        this.lastSecond = 0;
        
        // Performance thresholds
        this.thresholds = {
            lowFPS: 30,
            highFrameTime: 33.33, // 30fps
            highMemory: 100 * 1024 * 1024, // 100MB
            highLatency: 200
        };
        
        // Auto-optimization
        this.autoOptimize = true;
        this.optimizationLevel = 0; // 0 = full quality, 3 = maximum optimization
        
        console.log('PerformanceMonitor: Initialized');
    }

    enable() {
        if (this.enabled) return;
        
        this.enabled = true;
        this.startMonitoring();
        
        console.log('PerformanceMonitor: Enabled');
    }

    disable() {
        if (!this.enabled) return;
        
        this.enabled = false;
        this.stopMonitoring();
        
        console.log('PerformanceMonitor: Disabled');
    }

    startMonitoring() {
        // Start performance monitoring loop
        this.monitoringInterval = setInterval(() => {
            this.updateMetrics();
            this.checkPerformance();
        }, 1000);
    }

    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    update(time, delta) {
        if (!this.enabled) return;
        
        // Update frame timing
        const currentTime = performance.now();
        const frameTime = currentTime - this.lastFrameTime;
        this.lastFrameTime = currentTime;
        
        // Track frame time
        this.frameTimeHistory.push(frameTime);
        if (this.frameTimeHistory.length > this.maxHistoryLength) {
            this.frameTimeHistory.shift();
        }
        
        // Calculate FPS
        this.frameCount++;
        if (currentTime - this.lastSecond >= 1000) {
            this.metrics.fps = this.frameCount;
            this.fpsHistory.push(this.metrics.fps);
            
            if (this.fpsHistory.length > this.maxHistoryLength) {
                this.fpsHistory.shift();
            }
            
            this.frameCount = 0;
            this.lastSecond = currentTime;
            
            // Emit FPS update
            this.scene.events.emit('fpsUpdate', this.metrics.fps);
        }
        
        // Update average frame time
        if (this.frameTimeHistory.length > 0) {
            this.metrics.frameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
        }
    }

    updateMetrics() {
        // Update memory usage
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize;
        }
        
        // Update network latency
        if (this.scene.networkManager) {
            this.metrics.networkLatency = this.scene.networkManager.getLatency();
        }
        
        // Count active objects
        this.metrics.activeObjects = this.countActiveObjects();
        
        // Count render calls (approximation)
        this.metrics.renderCalls = this.estimateRenderCalls();
    }

    countActiveObjects() {
        let count = 0;
        
        // Count snakes
        if (this.scene.mySnake) count++;
        count += this.scene.otherSnakes.length;
        
        // Count food
        if (this.scene.foodManager) {
            count += this.scene.foodManager.getFoodCount();
        }
        
        // Count particles
        if (this.scene.particleEffects) {
            count += this.scene.particleEffects.effects.size;
        }
        
        return count;
    }

    estimateRenderCalls() {
        // Rough estimation based on active objects
        let calls = 0;
        
        // Base UI calls
        calls += 10;
        
        // Snake rendering
        calls += this.metrics.activeObjects * 2; // Head + body segments
        
        // Food rendering
        if (this.scene.foodManager) {
            calls += this.scene.foodManager.getFoodCount();
        }
        
        // Particle effects
        if (this.scene.particleEffects) {
            calls += this.scene.particleEffects.effects.size * 5;
        }
        
        return calls;
    }

    checkPerformance() {
        const issues = [];
        
        // Check FPS
        if (this.metrics.fps < this.thresholds.lowFPS) {
            issues.push({
                type: 'lowFPS',
                severity: 'high',
                value: this.metrics.fps,
                threshold: this.thresholds.lowFPS
            });
        }
        
        // Check frame time
        if (this.metrics.frameTime > this.thresholds.highFrameTime) {
            issues.push({
                type: 'highFrameTime',
                severity: 'medium',
                value: this.metrics.frameTime,
                threshold: this.thresholds.highFrameTime
            });
        }
        
        // Check memory usage
        if (this.metrics.memoryUsage > this.thresholds.highMemory) {
            issues.push({
                type: 'highMemory',
                severity: 'medium',
                value: this.metrics.memoryUsage,
                threshold: this.thresholds.highMemory
            });
        }
        
        // Check network latency
        if (this.metrics.networkLatency > this.thresholds.highLatency) {
            issues.push({
                type: 'highLatency',
                severity: 'low',
                value: this.metrics.networkLatency,
                threshold: this.thresholds.highLatency
            });
        }
        
        // Auto-optimize if needed
        if (this.autoOptimize && issues.length > 0) {
            this.autoOptimizePerformance(issues);
        }
        
        // Emit performance update
        this.scene.events.emit('performanceUpdate', {
            metrics: this.metrics,
            issues: issues,
            optimizationLevel: this.optimizationLevel
        });
    }

    autoOptimizePerformance(issues) {
        const highSeverityIssues = issues.filter(issue => issue.severity === 'high');
        
        if (highSeverityIssues.length > 0 && this.optimizationLevel < 3) {
            this.optimizationLevel++;
            this.applyOptimizations();
            
            console.log(`PerformanceMonitor: Auto-optimization level ${this.optimizationLevel} applied`);
        }
    }

    applyOptimizations() {
        switch (this.optimizationLevel) {
            case 1:
                // Level 1: Reduce particle effects
                this.optimizeParticles(0.7);
                break;
                
            case 2:
                // Level 2: Reduce food count and disable some effects
                this.optimizeFood(0.8);
                this.optimizeParticles(0.5);
                break;
                
            case 3:
                // Level 3: Maximum optimization
                this.optimizeFood(0.6);
                this.optimizeParticles(0.3);
                this.optimizeRendering();
                break;
        }
    }

    optimizeParticles(factor) {
        if (this.scene.particleEffects) {
            // Reduce particle counts
            Object.keys(this.scene.particleEffects.configs).forEach(key => {
                const config = this.scene.particleEffects.configs[key];
                config.quantity = Math.floor(config.quantity * factor);
                config.lifespan = Math.floor(config.lifespan * 0.8);
            });
        }
    }

    optimizeFood(factor) {
        if (this.scene.foodManager) {
            const newMaxFood = Math.floor(1000 * factor);
            this.scene.foodManager.setMaxFood(newMaxFood);
        }
    }

    optimizeRendering() {
        // Reduce render resolution on mobile
        if (this.scene.game.isMobile) {
            this.scene.game.renderer.resolution = 0.6;
        }
        
        // Disable some visual effects
        this.scene.cameras.main.setRoundPixels(true);
    }

    // Manual optimization controls
    setOptimizationLevel(level) {
        this.optimizationLevel = Math.max(0, Math.min(3, level));
        this.applyOptimizations();
    }

    resetOptimizations() {
        this.optimizationLevel = 0;
        
        // Reset to default values
        if (this.scene.particleEffects) {
            this.scene.particleEffects.init(); // Reinitialize with default configs
        }
        
        if (this.scene.foodManager) {
            this.scene.foodManager.setMaxFood(1000);
        }
        
        if (this.scene.game.isMobile) {
            this.scene.game.renderer.resolution = 0.8;
        }
    }

    // Getters
    getMetrics() {
        return { ...this.metrics };
    }

    getAverageFPS() {
        if (this.fpsHistory.length === 0) return 60;
        return this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
    }

    getAverageFrameTime() {
        if (this.frameTimeHistory.length === 0) return 16.67;
        return this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
    }

    getPerformanceGrade() {
        const avgFPS = this.getAverageFPS();
        
        if (avgFPS >= 55) return 'A';
        if (avgFPS >= 45) return 'B';
        if (avgFPS >= 30) return 'C';
        if (avgFPS >= 20) return 'D';
        return 'F';
    }

    // Performance reporting
    generateReport() {
        return {
            timestamp: Date.now(),
            metrics: this.getMetrics(),
            averages: {
                fps: this.getAverageFPS(),
                frameTime: this.getAverageFrameTime()
            },
            grade: this.getPerformanceGrade(),
            optimizationLevel: this.optimizationLevel,
            deviceInfo: {
                isMobile: this.scene.game.isMobile,
                userAgent: navigator.userAgent,
                memory: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null
            }
        };
    }

    // Settings
    setAutoOptimize(enabled) {
        this.autoOptimize = enabled;
    }

    setThreshold(type, value) {
        if (this.thresholds.hasOwnProperty(type)) {
            this.thresholds[type] = value;
        }
    }

    // Cleanup
    destroy() {
        this.disable();
        
        this.fpsHistory = [];
        this.frameTimeHistory = [];
        
        console.log('PerformanceMonitor: Destroyed');
    }
}
