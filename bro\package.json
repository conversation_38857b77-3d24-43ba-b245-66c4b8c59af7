{"name": "slither-io-clone", "version": "1.0.0", "description": "A Slither.io clone with real-time multiplayer gameplay using Phaser.js", "main": "server.js", "scripts": {"start": "npm run build && node server.js", "dev": "npm run build-dev && nodemon server.js", "build": "webpack --mode production", "build-dev": "webpack --mode development", "watch": "webpack --mode development --watch", "test-phase2": "node test-build.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "uuid": "^9.0.0", "phaser": "^3.70.0"}, "devDependencies": {"nodemon": "^3.0.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "babel-loader": "^9.1.0", "@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "html-webpack-plugin": "^5.5.0", "copy-webpack-plugin": "^11.0.0"}, "keywords": ["game", "multiplayer", "snake", "slither", "phaser"], "author": "Your Name", "license": "MIT"}