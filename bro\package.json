{"name": "slither-io-clone", "version": "1.0.0", "description": "A Slither.io clone with real-time multiplayer gameplay using Phaser.js", "main": "server.js", "scripts": {"start": "npm run build && node server.js", "dev": "npm run build-dev && nodemon server.js", "build": "webpack --mode production", "build-dev": "webpack --mode development", "watch": "webpack --mode development --watch", "test-phase2": "node test-build.js", "test-phase3": "node test-phase3.js", "deploy": "npm run build && pm2 restart ecosystem.config.js --env production", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:status": "pm2 status", "verify": "node verify-deployment.js", "verify:domain": "node verify-deployment.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "uuid": "^9.0.0", "phaser": "^3.70.0", "compression": "^1.7.4", "helmet": "^7.0.0", "cors": "^2.8.5", "pm2": "^5.3.0"}, "devDependencies": {"nodemon": "^3.0.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "babel-loader": "^9.1.0", "@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "html-webpack-plugin": "^5.5.0", "copy-webpack-plugin": "^11.0.0", "compression-webpack-plugin": "^10.0.0", "terser-webpack-plugin": "^5.3.0"}, "keywords": ["game", "multiplayer", "snake", "slither", "phaser"], "author": "Your Name", "license": "MIT"}