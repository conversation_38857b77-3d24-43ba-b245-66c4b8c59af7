{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "main": "server.js", "scripts": {"dev": "webpack", "deploy": "webpack --config webpack.production.config.js", "start": "node server", "build": "rimraf dist && cross-env NODE_ENV=production webpack --config ./webpack.production.config.js --progress --profile --colors", "test": "standard"}, "license": "ISC", "devDependencies": {"babel-core": "^6.8.0", "babel-loader": "^6.2.3", "babel-polyfill": "^6.8.0", "babel-preset-es2015": "^6.5.0", "expose-loader": "^0.7.1", "express": "^4.13.3", "extract-text-webpack-plugin": "^0.8.2", "file-loader": "^0.8.5", "html-webpack-plugin": "^1.6.1", "standard": "^7.0.1", "webfontloader": "^1.6.21", "webpack": "^1.12.13", "webpack-dev-middleware": "^1.2.0", "webpack-hot-middleware": "^2.2.0"}, "dependencies": {"cross-env": "^7.0.3", "phaser": "^2.4.7", "rimraf": "^6.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}}