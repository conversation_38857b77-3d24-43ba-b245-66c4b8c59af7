#!/bin/bash

# Slither.io Game Deployment Script
# Usage: ./deploy.sh [production|staging]

set -e  # Exit on any error

# Configuration
ENVIRONMENT=${1:-production}
APP_NAME="slither-game"
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"
LOG_FILE="/var/log/$APP_NAME-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a $LOG_FILE
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root. Use a deploy user with sudo privileges."
    fi
    
    if ! sudo -n true 2>/dev/null; then
        error "This script requires sudo privileges. Please run with a user that has sudo access."
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js 18+ first."
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        error "Node.js version 18+ is required. Current version: $(node --version)"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed."
    fi
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        warning "PM2 is not installed. Installing PM2..."
        sudo npm install -g pm2
    fi
    
    # Check nginx
    if ! command -v nginx &> /dev/null; then
        warning "Nginx is not installed. Please install nginx manually."
    fi
    
    log "✅ System requirements check passed"
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    
    sudo mkdir -p $APP_DIR
    sudo mkdir -p $BACKUP_DIR
    sudo mkdir -p /var/log/$APP_NAME
    sudo mkdir -p $APP_DIR/logs
    
    # Set ownership
    sudo chown -R $USER:$USER $APP_DIR
    sudo chown -R $USER:$USER /var/log/$APP_NAME
    
    log "✅ Directories created"
}

# Backup current deployment
backup_current() {
    if [ -d "$APP_DIR" ] && [ "$(ls -A $APP_DIR)" ]; then
        log "Creating backup of current deployment..."
        
        BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
        sudo cp -r $APP_DIR $BACKUP_DIR/$BACKUP_NAME
        
        # Keep only last 5 backups
        sudo find $BACKUP_DIR -maxdepth 1 -type d -name "backup-*" | sort -r | tail -n +6 | sudo xargs rm -rf
        
        log "✅ Backup created: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# Deploy application
deploy_app() {
    log "Deploying application..."
    
    # Copy files
    cp -r . $APP_DIR/
    cd $APP_DIR
    
    # Install dependencies
    log "Installing dependencies..."
    npm ci --production
    
    # Build application
    log "Building application..."
    npm run build
    
    # Set permissions
    chmod +x $APP_DIR/deploy.sh
    
    log "✅ Application deployed"
}

# Configure PM2
setup_pm2() {
    log "Configuring PM2..."
    
    cd $APP_DIR
    
    # Stop existing processes
    pm2 stop $APP_NAME 2>/dev/null || true
    pm2 delete $APP_NAME 2>/dev/null || true
    
    # Start with ecosystem file
    pm2 start ecosystem.config.js --env $ENVIRONMENT
    
    # Save PM2 configuration
    pm2 save
    
    # Setup PM2 startup script
    sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u $USER --hp /home/<USER>
    
    log "✅ PM2 configured"
}

# Configure Nginx
setup_nginx() {
    log "Configuring Nginx..."
    
    # Copy nginx configuration
    sudo cp $APP_DIR/nginx.conf /etc/nginx/sites-available/$APP_NAME
    
    # Enable site
    sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
    
    # Test nginx configuration
    if sudo nginx -t; then
        sudo systemctl reload nginx
        log "✅ Nginx configured and reloaded"
    else
        error "Nginx configuration test failed"
    fi
}

# Setup SSL with Let's Encrypt (optional)
setup_ssl() {
    if command -v certbot &> /dev/null; then
        log "Setting up SSL certificate..."
        
        read -p "Enter your domain name: " DOMAIN
        if [ ! -z "$DOMAIN" ]; then
            sudo certbot --nginx -d $DOMAIN
            log "✅ SSL certificate configured"
        fi
    else
        warning "Certbot not installed. SSL setup skipped."
        info "To install SSL later, run: sudo apt install certbot python3-certbot-nginx"
    fi
}

# Health check
health_check() {
    log "Performing health check..."
    
    sleep 5  # Wait for application to start
    
    # Check if PM2 process is running
    if pm2 list | grep -q $APP_NAME; then
        log "✅ PM2 process is running"
    else
        error "PM2 process is not running"
    fi
    
    # Check if application responds
    if curl -f http://localhost:4000/health > /dev/null 2>&1; then
        log "✅ Application health check passed"
    else
        error "Application health check failed"
    fi
    
    # Check nginx status
    if sudo systemctl is-active --quiet nginx; then
        log "✅ Nginx is running"
    else
        warning "Nginx is not running"
    fi
}

# Main deployment function
main() {
    log "🚀 Starting deployment for environment: $ENVIRONMENT"
    
    check_permissions
    check_requirements
    setup_directories
    backup_current
    deploy_app
    setup_pm2
    
    if command -v nginx &> /dev/null; then
        setup_nginx
        
        if [ "$ENVIRONMENT" = "production" ]; then
            read -p "Do you want to setup SSL? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                setup_ssl
            fi
        fi
    fi
    
    health_check
    
    log "🎉 Deployment completed successfully!"
    log "🌍 Application is running at: http://localhost:4000"
    log "📊 Health check: http://localhost:4000/health"
    log "📈 PM2 status: pm2 status"
    log "📋 PM2 logs: pm2 logs $APP_NAME"
}

# Run main function
main "$@"
