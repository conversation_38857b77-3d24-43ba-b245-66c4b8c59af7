import Phaser from 'phaser';
import BootState from './states/BootState';
import GameState from './states/GameState';
import UIState from './states/UIState';

// Game configuration
const config = {
    type: Phaser.AUTO,
    width: window.innerWidth,
    height: window.innerHeight,
    parent: 'gameContainer',
    backgroundColor: '#0f0f23',
    scale: {
        mode: Phaser.Scale.RESIZE,
        autoCenter: Phaser.Scale.CENTER_BOTH
    },
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 },
            debug: false
        }
    },
    render: {
        antialias: true,
        pixelArt: false,
        roundPixels: true
    },
    input: {
        activePointer: {
            smoothFactor: 0.2
        }
    },
    scene: [BootState, GameState, UIState]
};

// Create game instance
class SlitherGame extends Phaser.Game {
    constructor() {
        super(config);
        
        // Global game properties
        this.gameConfig = {
            WORLD_WIDTH: 4000,
            WORLD_HEIGHT: 4000,
            SNAKE_SPEED: 300,        // Faster like original
            BOOST_SPEED: 600,        // Double speed boost
            TURN_SPEED: 200,         // Smooth turning
            SEGMENT_SIZE: 12,
            SEGMENT_SPACING: 8
        };
        
        // Network configuration
        this.networkConfig = {
            POSITION_UPDATE_RATE: 20,    // 20 FPS for position updates
            GAME_STATE_RATE: 10,         // 10 FPS for full game state
            LEADERBOARD_RATE: 2          // 2 FPS for leaderboard
        };
        
        // Mobile detection
        this.isMobile = this.detectMobile();
        
        // Adjust settings for mobile
        if (this.isMobile) {
            this.gameConfig.SNAKE_SPEED = 250;
            this.gameConfig.BOOST_SPEED = 500;
            this.renderer.resolution = 0.8; // Lower resolution for better performance
        }
        
        console.log('Slither.io Phaser Game Initialized');
        console.log('Mobile device:', this.isMobile);
    }
    
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
               window.innerWidth <= 768 || 
               ('ontouchstart' in window);
    }
}

// Initialize game when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.slitherGame = new SlitherGame();
});

export default SlitherGame;
